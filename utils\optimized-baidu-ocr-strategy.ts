// 优化的百度OCR识别策略 - 减少API调用，提高识别效率
export interface FrameInfo {
  timestamp: number
  imageData: ImageData
  hasSubtitle?: boolean
  subtitleText?: string
  confidence?: number
}

export interface SubtitleSegment {
  startTime: number
  endTime: number
  text: string
  confidence: number
  frameCount: number
  mergedImage?: string // base64编码的合并图像
}

export interface OptimizationConfig {
  // 动态采样配置
  initialSamplingRate: number // 初始采样率 (帧/秒)
  maxSamplingRate: number     // 最大采样率
  minSamplingRate: number     // 最小采样率
  
  // 字幕连续性检测
  stabilityFrames: number     // 字幕稳定判断帧数 (默认3帧)
  maxMergeFrames: number      // 最大合并帧数 (对应2-5秒)
  minMergeDuration: number    // 最小合并时长 (秒)
  maxMergeDuration: number    // 最大合并时长 (秒)
  
  // 相似度检测
  textSimilarityThreshold: number  // 文本相似度阈值
  imageSimilarityThreshold: number // 图像相似度阈值
  
  // OCR调用优化
  batchProcessing: boolean    // 是否启用批量处理
  cacheResults: boolean       // 是否缓存结果
  maxCacheSize: number        // 最大缓存大小
}

export class OptimizedBaiduOCRStrategy {
  private config: OptimizationConfig
  private frameBuffer: FrameInfo[] = []
  private currentSegment: SubtitleSegment | null = null
  private resultCache = new Map<string, any>()
  private lastSubtitleText = ''
  private stableFrameCount = 0
  private currentSamplingRate: number
  private anchors: any[] = [] // 存储锚点信息
  private lastApiCallTime = 0 // 上次API调用时间
  private apiCallDelay = 1000 // API调用间隔（毫秒）

  constructor(config: Partial<OptimizationConfig> = {}, anchors: any[] = []) {
    this.config = {
      initialSamplingRate: 1.5, // 降低初始采样率
      maxSamplingRate: 3,       // 降低最大采样率
      minSamplingRate: 0.5,
      stabilityFrames: 3,
      maxMergeFrames: 8,        // 减少最大合并帧数 (约2.7秒@3fps)
      minMergeDuration: 1.5,    // 减少最小合并时长
      maxMergeDuration: 3,      // 减少最大合并时长
      textSimilarityThreshold: 0.8,
      imageSimilarityThreshold: 0.9,
      batchProcessing: true,
      cacheResults: true,
      maxCacheSize: 50,         // 减少缓存大小
      ...config
    }
    this.currentSamplingRate = this.config.initialSamplingRate
    this.anchors = anchors.filter(anchor => anchor.isPrimary) // 只使用主锚点
  }

  /**
   * 动态帧采样 - 根据字幕变化频率调整采样率
   */
  async dynamicFrameSampling(
    videoElement: HTMLVideoElement,
    startTime: number,
    endTime: number,
    onProgress?: (progress: number) => void
  ): Promise<FrameInfo[]> {
    console.log(`🎯 开始动态帧采样: ${startTime}s - ${endTime}s`)
    
    const frames: FrameInfo[] = []
    let currentTime = startTime
    let lastChangeTime = startTime
    let noSubtitleCount = 0

    while (currentTime < endTime) {
      // 提取当前帧
      const frameInfo = await this.extractFrame(videoElement, currentTime)
      
      // 快速检测是否有字幕（基于图像特征）
      const hasSubtitle = await this.quickSubtitleDetection(frameInfo.imageData)
      frameInfo.hasSubtitle = hasSubtitle

      if (hasSubtitle) {
        frames.push(frameInfo)
        noSubtitleCount = 0
        lastChangeTime = currentTime

        // 检查内存使用
        if (!this.checkMemoryUsage() && frames.length > 10) {
          console.warn(`🛑 内存使用过高，停止采样`)
          break
        }

        // 检测到字幕，提高采样率
        this.currentSamplingRate = Math.min(
          this.currentSamplingRate * 1.2,
          this.config.maxSamplingRate
        )
      } else {
        noSubtitleCount++
        
        // 连续无字幕帧，降低采样率
        if (noSubtitleCount > 5) {
          this.currentSamplingRate = Math.max(
            this.currentSamplingRate * 0.8,
            this.config.minSamplingRate
          )
        }
      }

      // 计算下一帧时间
      const interval = 1 / this.currentSamplingRate
      currentTime += interval

      // 更新进度
      if (onProgress) {
        const progress = (currentTime - startTime) / (endTime - startTime)
        onProgress(Math.min(progress, 1))
      }
    }

    console.log(`✅ 动态采样完成: 共采样 ${frames.length} 帧，跳过 ${Math.floor((endTime - startTime) * this.config.initialSamplingRate) - frames.length} 无字幕帧`)
    return frames
  }

  /**
   * 字幕连续性检测和合并
   */
  async detectAndMergeSubtitleSegments(
    frames: FrameInfo[],
    ocrProcessor: any,
    onProgress?: (progress: number) => void
  ): Promise<SubtitleSegment[]> {
    console.log(`🔍 开始字幕连续性检测: ${frames.length} 帧`)
    
    const segments: SubtitleSegment[] = []
    let currentSegmentFrames: FrameInfo[] = []
    let lastText = ''
    let stableCount = 0

    for (let i = 0; i < frames.length; i++) {
      const frame = frames[i]
      
      // 对单帧进行OCR识别（用于连续性判断）- 使用锚点裁剪
      const ocrResults = await this.processImageWithAnchors(frame.imageData, ocrProcessor)
      const currentText = this.extractMainText(ocrResults)
      
      frame.subtitleText = currentText
      frame.confidence = this.calculateAverageConfidence(ocrResults)

      // 判断字幕是否发生变化
      const textSimilarity = this.calculateTextSimilarity(currentText, lastText)
      
      if (textSimilarity >= this.config.textSimilarityThreshold && currentText) {
        // 字幕内容相似，继续当前片段
        currentSegmentFrames.push(frame)
        stableCount++
      } else {
        // 字幕发生变化，处理当前片段
        if (currentSegmentFrames.length > 0) {
          const segment = await this.createMergedSegment(currentSegmentFrames, ocrProcessor)
          if (segment) {
            segments.push(segment)
          }
        }
        
        // 开始新片段
        currentSegmentFrames = currentText ? [frame] : []
        stableCount = 0
      }

      lastText = currentText

      // 检查是否达到最大合并帧数
      if (currentSegmentFrames.length >= this.config.maxMergeFrames) {
        const segment = await this.createMergedSegment(currentSegmentFrames, ocrProcessor)
        if (segment) {
          segments.push(segment)
        }
        currentSegmentFrames = []
      }

      // 更新进度
      if (onProgress) {
        onProgress((i + 1) / frames.length)
      }
    }

    // 处理最后一个片段
    if (currentSegmentFrames.length > 0) {
      const segment = await this.createMergedSegment(currentSegmentFrames, ocrProcessor)
      if (segment) {
        segments.push(segment)
      }
    }

    console.log(`✅ 字幕连续性检测完成: 生成 ${segments.length} 个字幕片段`)
    return segments
  }

  /**
   * 创建合并的字幕片段
   */
  private async createMergedSegment(
    frames: FrameInfo[],
    ocrProcessor: any
  ): Promise<SubtitleSegment | null> {
    if (frames.length === 0) return null

    const startTime = frames[0].timestamp
    const endTime = frames[frames.length - 1].timestamp
    const duration = endTime - startTime

    // 检查时长是否符合要求
    if (duration < this.config.minMergeDuration) {
      console.log(`⏭️ 跳过过短片段: ${duration.toFixed(1)}s < ${this.config.minMergeDuration}s`)
      return null
    }

    console.log(`🔗 合并字幕片段: ${frames.length} 帧, ${duration.toFixed(1)}s`)

    try {
      // 创建纵向拼接的长图
      const mergedImage = await this.createVerticalMergedImage(frames)
      
      // 对合并图像进行OCR识别 - 使用锚点裁剪
      const ocrResults = await this.processImageWithAnchors(mergedImage, ocrProcessor)
      const text = this.extractMainText(ocrResults)
      const confidence = this.calculateAverageConfidence(ocrResults)

      if (!text) {
        console.log(`⚠️ 合并片段无文本内容`)
        return null
      }

      return {
        startTime,
        endTime,
        text: text.trim(),
        confidence,
        frameCount: frames.length,
        mergedImage: await this.imageDataToBase64(mergedImage)
      }
    } catch (error) {
      console.error(`❌ 创建合并片段失败:`, error)
      return null
    }
  }

  /**
   * 创建纵向拼接的长图（带内存优化）
   */
  private async createVerticalMergedImage(frames: FrameInfo[]): Promise<ImageData> {
    if (frames.length === 0) {
      throw new Error('无帧数据用于合并')
    }

    const firstFrame = frames[0].imageData
    let width = firstFrame.width
    let frameHeight = firstFrame.height

    // 内存限制检查和图像压缩
    const maxCanvasSize = 16384 // 浏览器画布最大尺寸限制
    const maxMemoryMB = 100 // 最大内存使用限制 (MB)

    // 计算原始尺寸的内存使用
    const originalTotalHeight = frameHeight * frames.length
    const originalMemoryMB = (width * originalTotalHeight * 4) / (1024 * 1024) // RGBA = 4 bytes per pixel

    console.log(`📊 图像合并分析: ${frames.length}帧, 原始尺寸: ${width}x${originalTotalHeight}, 预计内存: ${originalMemoryMB.toFixed(1)}MB`)

    // 如果超过限制，进行压缩
    let scaleFactor = 1
    if (originalTotalHeight > maxCanvasSize || originalMemoryMB > maxMemoryMB) {
      // 计算缩放因子
      const heightScale = maxCanvasSize / originalTotalHeight
      const memoryScale = Math.sqrt(maxMemoryMB / originalMemoryMB)
      scaleFactor = Math.min(heightScale, memoryScale, 0.5) // 最大压缩到50%

      width = Math.floor(width * scaleFactor)
      frameHeight = Math.floor(frameHeight * scaleFactor)

      console.log(`🔧 应用压缩: 缩放因子=${scaleFactor.toFixed(2)}, 新尺寸: ${width}x${frameHeight * frames.length}`)
    }

    const totalHeight = frameHeight * frames.length

    // 检查最终尺寸是否合理
    if (totalHeight > maxCanvasSize) {
      // 如果还是太大，减少帧数
      const maxFrames = Math.floor(maxCanvasSize / frameHeight)
      frames = frames.slice(0, maxFrames)
      console.log(`⚠️ 帧数过多，限制为 ${maxFrames} 帧`)
    }

    const finalTotalHeight = frameHeight * frames.length
    const finalMemoryMB = (width * finalTotalHeight * 4) / (1024 * 1024)

    console.log(`✅ 最终合并尺寸: ${width}x${finalTotalHeight}, 内存使用: ${finalMemoryMB.toFixed(1)}MB`)

    try {
      // 创建画布
      const canvas = document.createElement('canvas')
      const ctx = canvas.getContext('2d')!
      canvas.width = width
      canvas.height = finalTotalHeight

      // 逐帧绘制到画布上
      for (let i = 0; i < frames.length; i++) {
        const frame = frames[i]
        const yOffset = i * frameHeight

        // 创建临时画布来处理ImageData
        const tempCanvas = document.createElement('canvas')
        const tempCtx = tempCanvas.getContext('2d')!
        tempCanvas.width = frame.imageData.width
        tempCanvas.height = frame.imageData.height

        tempCtx.putImageData(frame.imageData, 0, 0)

        // 如果需要缩放，使用drawImage进行缩放
        if (scaleFactor !== 1) {
          ctx.drawImage(tempCanvas, 0, yOffset, width, frameHeight)
        } else {
          ctx.drawImage(tempCanvas, 0, yOffset)
        }

        // 清理临时画布以释放内存
        tempCanvas.width = 0
        tempCanvas.height = 0
      }

      // 获取合并后的ImageData
      const mergedImageData = ctx.getImageData(0, 0, width, finalTotalHeight)

      // 清理画布
      canvas.width = 0
      canvas.height = 0

      return mergedImageData

    } catch (error) {
      console.error('❌ 图像合并失败:', error)

      // 如果还是失败，尝试更激进的压缩
      if (scaleFactor > 0.2) {
        console.log('🔄 尝试更激进的压缩...')
        const reducedFrames = frames.slice(0, Math.max(1, Math.floor(frames.length / 2)))
        return this.createVerticalMergedImage(reducedFrames)
      }

      throw new Error(`图像合并失败: ${error instanceof Error ? error.message : '内存不足'}`)
    }
  }

  /**
   * 动态检测字幕文本边界
   */
  private detectTextBounds(imageData: ImageData, anchor: any): {
    left: number
    right: number
    top: number
    bottom: number
  } {
    const { width, height, data } = imageData

    // 基于锚点的初始搜索区域（修复计算错误）
    const searchLeft = Math.max(0, Math.floor(anchor.centerX - anchor.width * 1.5))
    const searchRight = Math.min(width, Math.floor(anchor.centerX + anchor.width * 1.5))
    const searchTop = Math.max(0, Math.floor(anchor.centerY - anchor.height / 2))
    const searchBottom = Math.min(height, Math.floor(anchor.centerY + anchor.height / 2))

    console.log(`🔍 检测锚点 ${anchor.id} 文本边界:`, {
      锚点: { x: anchor.centerX, y: anchor.centerY, w: anchor.width, h: anchor.height },
      搜索区域: { left: searchLeft, right: searchRight, top: searchTop, bottom: searchBottom },
      图像尺寸: { width, height }
    })

    let textLeft = searchRight
    let textRight = searchLeft
    let textTop = searchBottom
    let textBottom = searchTop

    // 扫描搜索区域，寻找文本像素
    for (let y = searchTop; y < searchBottom; y++) {
      for (let x = searchLeft; x < searchRight; x++) {
        const index = (y * width + x) * 4
        const r = data[index]
        const g = data[index + 1]
        const b = data[index + 2]

        // 检测高对比度像素（可能是文字）
        const brightness = (r + g + b) / 3
        const isTextPixel = brightness < 100 || brightness > 200

        if (isTextPixel) {
          textLeft = Math.min(textLeft, x)
          textRight = Math.max(textRight, x)
          textTop = Math.min(textTop, y)
          textBottom = Math.max(textBottom, y)
        }
      }
    }

    // 如果没有检测到文本，使用原始锚点
    if (textLeft >= textRight || textTop >= textBottom) {
      console.log(`⚠️ 锚点 ${anchor.id} 未检测到文本，使用原始边界`)
      return {
        left: searchLeft,
        right: searchRight,
        top: searchTop,
        bottom: searchBottom
      }
    }

    // 添加一些边距
    const margin = 10
    return {
      left: Math.max(0, textLeft - margin),
      right: Math.min(width, textRight + margin),
      top: Math.max(0, textTop - margin),
      bottom: Math.min(height, textBottom + margin)
    }
  }

  /**
   * 裁剪锚点区域（支持动态调整）
   */
  private cropAnchorRegion(imageData: ImageData, anchor: any): {
    croppedImage: ImageData
    offsetX: number
    offsetY: number
    dynamicBounds?: any
  } {
    const { width, height } = imageData

    // 动态检测文本边界
    const bounds = this.detectTextBounds(imageData, anchor)

    const cropWidth = bounds.right - bounds.left
    const cropHeight = bounds.bottom - bounds.top

    console.log(`✂️ 裁剪锚点 ${anchor.id} 区域:`, {
      边界: bounds,
      裁剪尺寸: { width: cropWidth, height: cropHeight }
    })

    // 验证裁剪尺寸
    if (cropWidth <= 0 || cropHeight <= 0) {
      console.error(`❌ 锚点 ${anchor.id} 裁剪尺寸无效:`, { cropWidth, cropHeight })
      throw new Error(`无效的裁剪尺寸: ${cropWidth}x${cropHeight}`)
    }

    // 创建裁剪后的ImageData
    const canvas = document.createElement('canvas')
    const ctx = canvas.getContext('2d')!
    canvas.width = cropWidth
    canvas.height = cropHeight

    // 绘制原图像的裁剪区域
    ctx.putImageData(imageData, -bounds.left, -bounds.top)

    // 获取裁剪后的ImageData
    const croppedImage = ctx.getImageData(0, 0, cropWidth, cropHeight)

    console.log(`✅ 锚点 ${anchor.id} 裁剪完成，裁剪图像尺寸: ${croppedImage.width}x${croppedImage.height}`)

    return {
      croppedImage,
      offsetX: bounds.left,
      offsetY: bounds.top,
      dynamicBounds: bounds
    }
  }

  /**
   * API调用频率控制
   */
  private async waitForApiDelay(): Promise<void> {
    const now = Date.now()
    const timeSinceLastCall = now - this.lastApiCallTime

    if (timeSinceLastCall < this.apiCallDelay) {
      const waitTime = this.apiCallDelay - timeSinceLastCall
      console.log(`⏱️ API频率控制，等待 ${waitTime}ms`)
      await new Promise(resolve => setTimeout(resolve, waitTime))
    }

    this.lastApiCallTime = Date.now()
  }

  /**
   * 使用锚点进行OCR识别
   */
  private async processImageWithAnchors(imageData: ImageData, ocrProcessor: any): Promise<any[]> {
    console.log(`🎯 开始处理图像，锚点数量: ${this.anchors.length}`)

    if (this.anchors.length === 0) {
      // 如果没有锚点，直接对整个图像进行OCR
      console.log(`⚠️ 没有锚点，对整个图像进行OCR`)
      await this.waitForApiDelay()
      const results = await ocrProcessor.processImage(imageData)
      console.log(`✅ 整图OCR完成，识别到 ${results.length} 个文本`)
      return results
    }

    const allResults: any[] = []

    for (const anchor of this.anchors) {
      try {
        // API频率控制
        await this.waitForApiDelay()

        // 裁剪锚点区域
        const { croppedImage } = this.cropAnchorRegion(imageData, anchor)

        // 对裁剪区域进行OCR识别
        console.log(`🔍 正在调用百度OCR API识别锚点 ${anchor.id}...`)
        const results = await ocrProcessor.processImage(croppedImage)
        console.log(`✅ 百度OCR API调用成功，锚点 ${anchor.id} 识别到 ${results.length} 个文本`)

        // 将结果添加到总结果中
        allResults.push(...results)
      } catch (error) {
        console.warn(`锚点 ${anchor.id} OCR识别失败:`, error)

        // 如果是QPS限制错误，增加延迟
        if (error.message && error.message.includes('qps request limit')) {
          console.log('🚫 检测到QPS限制，增加API调用延迟')
          this.apiCallDelay = Math.min(this.apiCallDelay * 2, 5000) // 最大5秒
        }
      }
    }

    return allResults
  }

  /**
   * 快速字幕检测（基于图像特征，无需OCR）
   */
  private async quickSubtitleDetection(imageData: ImageData): Promise<boolean> {
    // 简单的字幕区域检测算法
    // 检查图像底部区域是否有文本特征
    const { width, height, data } = imageData
    const bottomRegionHeight = Math.floor(height * 0.2) // 底部20%区域
    const startY = height - bottomRegionHeight
    
    let textPixelCount = 0
    let totalPixels = 0

    for (let y = startY; y < height; y++) {
      for (let x = 0; x < width; x++) {
        const index = (y * width + x) * 4
        const r = data[index]
        const g = data[index + 1]
        const b = data[index + 2]
        
        // 检测高对比度像素（可能是文字）
        const brightness = (r + g + b) / 3
        const isHighContrast = brightness < 50 || brightness > 200
        
        if (isHighContrast) {
          textPixelCount++
        }
        totalPixels++
      }
    }

    const textRatio = textPixelCount / totalPixels
    return textRatio > 0.1 // 如果高对比度像素超过10%，认为可能有字幕
  }

  /**
   * 提取主要文本内容
   */
  private extractMainText(ocrResults: any[]): string {
    console.log(`📝 提取主要文本，OCR结果数量: ${ocrResults?.length || 0}`)

    if (!ocrResults || ocrResults.length === 0) {
      console.log(`⚠️ 没有OCR结果`)
      return ''
    }

    // 打印所有OCR结果
    ocrResults.forEach((result, index) => {
      console.log(`📄 OCR结果 ${index + 1}:`, {
        text: result.text,
        confidence: result.confidence,
        bbox: result.bbox || result.location
      })
    })

    // 按置信度排序，取最可信的文本
    const sortedResults = ocrResults
      .filter(result => result.confidence > 0.5)
      .sort((a, b) => b.confidence - a.confidence)

    const extractedText = sortedResults.map(result => result.text).join(' ').trim()
    console.log(`✅ 提取的主要文本: "${extractedText}"`)

    return extractedText
  }

  /**
   * 计算平均置信度
   */
  private calculateAverageConfidence(ocrResults: any[]): number {
    if (!ocrResults || ocrResults.length === 0) return 0
    
    const confidences = ocrResults.map(result => result.confidence || 0)
    return confidences.reduce((sum, conf) => sum + conf, 0) / confidences.length
  }

  /**
   * 计算文本相似度
   */
  private calculateTextSimilarity(text1: string, text2: string): number {
    if (!text1 || !text2) return 0
    if (text1 === text2) return 1
    
    // 简单的编辑距离相似度计算
    const maxLength = Math.max(text1.length, text2.length)
    if (maxLength === 0) return 1
    
    const distance = this.levenshteinDistance(text1, text2)
    return 1 - distance / maxLength
  }

  /**
   * 计算编辑距离
   */
  private levenshteinDistance(str1: string, str2: string): number {
    const matrix = []
    
    for (let i = 0; i <= str2.length; i++) {
      matrix[i] = [i]
    }
    
    for (let j = 0; j <= str1.length; j++) {
      matrix[0][j] = j
    }
    
    for (let i = 1; i <= str2.length; i++) {
      for (let j = 1; j <= str1.length; j++) {
        if (str2.charAt(i - 1) === str1.charAt(j - 1)) {
          matrix[i][j] = matrix[i - 1][j - 1]
        } else {
          matrix[i][j] = Math.min(
            matrix[i - 1][j - 1] + 1,
            matrix[i][j - 1] + 1,
            matrix[i - 1][j] + 1
          )
        }
      }
    }
    
    return matrix[str2.length][str1.length]
  }

  /**
   * 提取视频帧（带尺寸优化）
   */
  private async extractFrame(video: HTMLVideoElement, timestamp: number): Promise<FrameInfo> {
    return new Promise((resolve, reject) => {
      const canvas = document.createElement('canvas')
      const ctx = canvas.getContext('2d')!

      video.currentTime = timestamp

      video.onseeked = () => {
        try {
          let width = video.videoWidth
          let height = video.videoHeight

          // 限制单帧最大尺寸以避免内存问题
          const maxDimension = 1920 // 最大宽度或高度
          const maxPixels = 1920 * 1080 // 最大像素数 (Full HD)

          const currentPixels = width * height

          if (width > maxDimension || height > maxDimension || currentPixels > maxPixels) {
            // 计算缩放因子
            const dimensionScale = Math.min(maxDimension / width, maxDimension / height)
            const pixelScale = Math.sqrt(maxPixels / currentPixels)
            const scaleFactor = Math.min(dimensionScale, pixelScale)

            width = Math.floor(width * scaleFactor)
            height = Math.floor(height * scaleFactor)

            console.log(`🔧 帧尺寸优化: ${video.videoWidth}x${video.videoHeight} → ${width}x${height} (缩放: ${scaleFactor.toFixed(2)})`)
          }

          canvas.width = width
          canvas.height = height

          // 绘制视频帧（如果需要缩放）
          if (width !== video.videoWidth || height !== video.videoHeight) {
            ctx.drawImage(video, 0, 0, width, height)
          } else {
            ctx.drawImage(video, 0, 0)
          }

          const imageData = ctx.getImageData(0, 0, width, height)

          // 清理画布
          canvas.width = 0
          canvas.height = 0

          resolve({
            timestamp,
            imageData
          })
        } catch (error) {
          console.error(`❌ 提取帧失败 (${timestamp}s):`, error)
          reject(new Error(`提取帧失败: ${timestamp}s - ${error instanceof Error ? error.message : '未知错误'}`))
        }
      }

      video.onerror = () => reject(new Error(`视频错误: ${timestamp}s`))

      // 添加超时处理
      setTimeout(() => {
        reject(new Error(`提取帧超时: ${timestamp}s`))
      }, 5000)
    })
  }

  /**
   * ImageData转Base64
   */
  private async imageDataToBase64(imageData: ImageData): Promise<string> {
    const canvas = document.createElement('canvas')
    const ctx = canvas.getContext('2d')!
    
    canvas.width = imageData.width
    canvas.height = imageData.height
    ctx.putImageData(imageData, 0, 0)
    
    return canvas.toDataURL('image/png').split(',')[1]
  }

  /**
   * 获取优化统计信息
   */
  getOptimizationStats() {
    // 估算内存使用
    const estimatedMemoryMB = this.frameBuffer.reduce((total, frame) => {
      const frameMemory = (frame.imageData.width * frame.imageData.height * 4) / (1024 * 1024)
      return total + frameMemory
    }, 0)

    return {
      currentSamplingRate: this.currentSamplingRate,
      cacheSize: this.resultCache.size,
      frameBufferSize: this.frameBuffer.length,
      estimatedMemoryMB: estimatedMemoryMB.toFixed(1),
      memoryWarning: estimatedMemoryMB > 50 // 超过50MB发出警告
    }
  }

  /**
   * 检查内存使用情况
   */
  private checkMemoryUsage(): boolean {
    const stats = this.getOptimizationStats()
    if (stats.memoryWarning) {
      console.warn(`⚠️ 内存使用过高: ${stats.estimatedMemoryMB}MB，建议清理缓存`)
      return false
    }
    return true
  }

  /**
   * 清理缓存
   */
  clearCache() {
    this.resultCache.clear()
    this.frameBuffer = []
    console.log('🧹 OCR优化策略缓存已清理')
  }
}
