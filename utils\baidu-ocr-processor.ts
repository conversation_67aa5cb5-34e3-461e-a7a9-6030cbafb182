// 百度OCR处理器 - 调用百度智能云OCR API
export interface OCRResult {
  text: string
  confidence: number
  bbox: number[][]
}

export class BaiduOCRProcessor {
  private isInitialized = false
  private baseUrl: string
  private apiVersion: 'basic' | 'accurate' | 'location'

  constructor(apiVersion: 'basic' | 'accurate' | 'location' = 'location') {
    this.baseUrl = typeof window !== 'undefined' ? window.location.origin : 'http://localhost:3000'
    this.apiVersion = apiVersion
  }

  async initialize(): Promise<void> {
    try {
      console.log('正在初始化百度OCR处理器...')

      // 测试后端百度OCR API是否可用
      const response = await fetch(`${this.baseUrl}/api/ocr/test-baidu`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          apiKey: 'test',
          secretKey: 'test'
        })
      })

      // 检查API端点是否存在（即使密钥错误也说明端点可用）
      if (response.status === 400 || response.status === 200) {
        this.isInitialized = true
        console.log('✅ 百度OCR处理器初始化完成（通过后端API）')
      } else {
        throw new Error('后端百度OCR API不可用')
      }
    } catch (error) {
      console.error('百度OCR处理器初始化失败:', error)
      throw new Error('无法连接到后端百度OCR服务，请确保API密钥已在设置中配置')
    }
  }



  async processImage(imageData: ImageData): Promise<OCRResult[]> {
    if (!this.isInitialized) {
      throw new Error('处理器未初始化')
    }

    try {
      console.log(`🔍 通过后端API调用百度OCR识别图像 (${imageData.width}x${imageData.height})`)

      // 将ImageData转换为压缩的Blob
      const canvas = document.createElement('canvas')
      const ctx = canvas.getContext('2d')!

      canvas.width = imageData.width
      canvas.height = imageData.height
      ctx.putImageData(imageData, 0, 0)

      // 转换为压缩的JPEG格式以减少文件大小
      let blob = await new Promise<Blob>((resolve) => {
        canvas.toBlob((blob) => {
          resolve(blob!)
        }, 'image/jpeg', 0.8) // 使用JPEG格式，质量80%
      })

      // 如果文件仍然太大，进一步压缩
      const maxSize = 8 * 1024 * 1024 // 8MB限制
      if (blob.size > maxSize) {
        console.log(`📦 图像文件过大 (${(blob.size / 1024 / 1024).toFixed(2)}MB)，进行进一步压缩...`)

        // 降低质量进行压缩
        blob = await new Promise<Blob>((resolve) => {
          canvas.toBlob((blob) => {
            resolve(blob!)
          }, 'image/jpeg', 0.5) // 降低到50%质量
        })

        console.log(`📦 压缩后文件大小: ${(blob.size / 1024 / 1024).toFixed(2)}MB`)
      }

      // 创建FormData
      const formData = new FormData()
      formData.append('image', blob, 'frame.png')

      // 根据API版本选择不同的端点
      const apiEndpoint = this.getApiEndpoint()

      // 获取API密钥（从localStorage）
      const apiKey = typeof window !== 'undefined' ? localStorage.getItem('baidu_ocr_api_key') : null
      const secretKey = typeof window !== 'undefined' ? localStorage.getItem('baidu_ocr_secret_key') : null

      // 准备请求头
      const headers: Record<string, string> = {}
      if (apiKey && secretKey) {
        headers['x-baidu-api-key'] = apiKey
        headers['x-baidu-secret-key'] = secretKey
      }

      // 发送到后端百度OCR API
      const response = await fetch(`${this.baseUrl}${apiEndpoint}`, {
        method: 'POST',
        headers: headers,
        body: formData
      })

      if (!response.ok) {
        const errorText = await response.text()
        console.error(`❌ 后端百度OCR API请求失败: ${response.status} - ${errorText}`)
        console.error(`请求URL: ${this.baseUrl}${apiEndpoint}`)
        console.error(`请求头:`, headers)

        // 尝试解析错误响应
        try {
          const errorData = JSON.parse(errorText)
          console.error(`错误详情:`, errorData)
          throw new Error(`后端百度OCR API请求失败: ${errorData.error || response.status}`)
        } catch (parseError) {
          throw new Error(`后端百度OCR API请求失败: ${response.status} - ${errorText}`)
        }
      }

      const result = await response.json()
      console.log(`📊 后端API响应:`, result)

      if (!result.success) {
        console.error(`❌ 百度OCR处理失败:`, result.error)

        // 如果是API密钥未配置的错误，提供更友好的提示
        if (result.error && result.error.includes('API密钥未配置')) {
          throw new Error('百度OCR API密钥未配置。请在设置页面配置您的百度OCR API Key和Secret Key。')
        }

        throw new Error(result.error || '百度OCR处理失败')
      }

      const ocrResults: OCRResult[] = result.results || []

      console.log(`✅ 百度OCR识别完成，共识别到 ${ocrResults.length} 个文本区域`)

      // 详细的结果分析
      if (ocrResults.length > 0) {
        const confidences = ocrResults.map(r => r.confidence)
        const avgConfidence = confidences.reduce((a, b) => a + b, 0) / confidences.length
        console.log(`📊 百度OCR识别质量:`, {
          '总数': ocrResults.length,
          '平均置信度': avgConfidence.toFixed(3),
          '高质量(>0.8)': ocrResults.filter(r => r.confidence > 0.8).length,
          '中等质量(0.6-0.8)': ocrResults.filter(r => r.confidence > 0.6 && r.confidence <= 0.8).length,
          '低质量(0.4-0.6)': ocrResults.filter(r => r.confidence > 0.4 && r.confidence <= 0.6).length
        })
      } else {
        console.warn('⚠️ 百度OCR未检测到任何文本')
      }

      return ocrResults
    } catch (error) {
      console.error('百度OCR处理失败:', error)
      throw error
    }
  }

  async detectAnchors(imageData: ImageData): Promise<{
    centerX: number
    centerY: number
    width: number
    height: number
    confidence: number
  }[]> {
    if (!this.isInitialized) {
      throw new Error('处理器未初始化')
    }

    try {
      // 先进行OCR识别
      const ocrResults = await this.processImage(imageData)
      
      // 基于OCR结果生成锚点
      const anchors = this.generateAnchorsFromOCR(ocrResults, imageData)
      
      console.log(`🎯 基于百度OCR结果生成了 ${anchors.length} 个锚点`)
      
      return anchors
    } catch (error) {
      console.error('百度OCR锚点检测失败:', error)
      throw error
    }
  }

  private generateAnchorsFromOCR(ocrResults: OCRResult[], imageData: ImageData): {
    centerX: number
    centerY: number
    width: number
    height: number
    confidence: number
  }[] {
    const anchors = []

    if (ocrResults.length === 0) {
      // 如果没有检测到文本，创建默认的底部字幕区域
      anchors.push({
        centerX: imageData.width * 0.5,
        centerY: imageData.height * 0.9,
        width: imageData.width * 0.8,
        height: imageData.height * 0.08,
        confidence: 0.8
      })
      return anchors
    }

    // 按Y坐标分组文本（识别不同行的字幕）
    const textGroups: Array<{
      texts: OCRResult[]
      avgY: number
      minX: number
      maxX: number
      minY: number
      maxY: number
    }> = []

    for (const result of ocrResults) {
      const bbox = result.bbox
      const centerY = (bbox[0][1] + bbox[2][1]) / 2
      const minX = Math.min(...bbox.map(p => p[0]))
      const maxX = Math.max(...bbox.map(p => p[0]))
      const minY = Math.min(...bbox.map(p => p[1]))
      const maxY = Math.max(...bbox.map(p => p[1]))

      // 查找相近的文本组
      let foundGroup = false
      for (const group of textGroups) {
        if (Math.abs(group.avgY - centerY) < 30) { // 30像素的Y轴容差
          group.texts.push(result)
          group.avgY = (group.avgY * (group.texts.length - 1) + centerY) / group.texts.length
          group.minX = Math.min(group.minX, minX)
          group.maxX = Math.max(group.maxX, maxX)
          group.minY = Math.min(group.minY, minY)
          group.maxY = Math.max(group.maxY, maxY)
          foundGroup = true
          break
        }
      }

      if (!foundGroup) {
        textGroups.push({
          texts: [result],
          avgY: centerY,
          minX,
          maxX,
          minY,
          maxY
        })
      }
    }

    // 为每个文本组创建锚点
    for (const group of textGroups) {
      const padding = 20
      const width = Math.max(group.maxX - group.minX + padding * 2, imageData.width * 0.3)
      const height = Math.max(group.maxY - group.minY + padding, 40)

      const centerX = (group.minX + group.maxX) / 2
      const centerY = (group.minY + group.maxY) / 2

      // 确保锚点在视频范围内
      const finalCenterX = Math.max(width / 2, Math.min(imageData.width - width / 2, centerX))
      const finalCenterY = Math.max(height / 2, Math.min(imageData.height - height / 2, centerY))

      const avgConfidence = group.texts.reduce((sum, text) => sum + text.confidence, 0) / group.texts.length

      anchors.push({
        centerX: finalCenterX,
        centerY: finalCenterY,
        width,
        height,
        confidence: avgConfidence
      })
    }

    return anchors
  }

  private async imageDataToBase64(imageData: ImageData): Promise<string> {
    return new Promise((resolve) => {
      const canvas = document.createElement('canvas')
      const ctx = canvas.getContext('2d')!
      
      canvas.width = imageData.width
      canvas.height = imageData.height
      ctx.putImageData(imageData, 0, 0)
      
      // 转换为base64，去掉data:image/png;base64,前缀
      const base64 = canvas.toDataURL('image/png').split(',')[1]
      resolve(base64)
    })
  }

  reset(): void {
    // 百度OCR处理器无需重置状态
  }

  dispose(): void {
    this.isInitialized = false
    console.log('百度OCR处理器已释放')
  }

  getStatus(): {
    isInitialized: boolean
    type: string
    version: string
    apiVersion: string
  } {
    return {
      isInitialized: this.isInitialized,
      type: 'Baidu OCR Processor',
      version: '1.0.0',
      apiVersion: this.getApiVersion()
    }
  }

  /**
   * 根据API版本获取对应的端点
   */
  private getApiEndpoint(): string {
    switch (this.apiVersion) {
      case 'basic':
        return '/api/ocr/baidu-process'
      case 'accurate':
        return '/api/ocr/baidu-process-accurate'
      case 'location':
        return '/api/ocr/baidu-process-location'
      default:
        return '/api/ocr/baidu-process-location' // 默认使用含位置版
    }
  }

  /**
   * 获取当前使用的API版本
   */
  getApiVersion(): string {
    const versionNames = {
      'basic': '通用文字识别（标准版）',
      'accurate': '通用文字识别（高精度版）',
      'location': '通用文字识别（高精度含位置版）'
    }
    return versionNames[this.apiVersion] || versionNames['location']
  }

  /**
   * 设置API版本
   */
  setApiVersion(version: 'basic' | 'accurate' | 'location'): void {
    this.apiVersion = version
    console.log(`🔄 百度OCR API版本已切换为: ${this.getApiVersion()}`)
  }
}
