import { NextApiRequest, NextApiResponse } from 'next'
import formidable from 'formidable'
import fs from 'fs'

// 禁用默认的body parser
export const config = {
  api: {
    bodyParser: false,
    responseLimit: false,
  },
}

interface BaiduOCRResponse {
  success: boolean
  results?: Array<{
    text: string
    confidence: number
    bbox: number[][]
  }>
  error?: string
}

// 缓存访问令牌
let cachedToken: string | null = null
let tokenExpireTime: number = 0

export default async function handler(req: NextApiRequest, res: NextApiResponse<BaiduOCRResponse>) {
  if (req.method !== 'POST') {
    return res.status(405).json({ success: false, error: '只支持POST请求' })
  }

  try {
    // 解析表单数据
    const form = formidable({
      maxFileSize: 20 * 1024 * 1024, // 20MB
      maxTotalFileSize: 20 * 1024 * 1024, // 20MB
      keepExtensions: true,
    })

    const [fields, files] = await form.parse(req)
    
    // 获取图像文件
    const imageFile = Array.isArray(files.image) ? files.image[0] : files.image
    if (!imageFile) {
      return res.status(400).json({ success: false, error: '缺少图像文件' })
    }

    // 优先从配置文件获取API密钥
    let apiKey = await getBaiduOcrApiKey()
    let secretKey = await getBaiduOcrSecretKey()

    // 如果配置文件中没有，则尝试从请求头获取（兼容localStorage场景）
    if (!apiKey || !secretKey) {
      apiKey = req.headers['x-baidu-api-key'] as string || apiKey
      secretKey = req.headers['x-baidu-secret-key'] as string || secretKey
    }

    console.log(`🔑 API密钥检查: apiKey=${apiKey ? '已配置' : '未配置'}, secretKey=${secretKey ? '已配置' : '未配置'}`)
    console.log(`🔍 密钥来源检查: 环境变量=${process.env.BAIDU_OCR_API_KEY ? '有' : '无'}, 配置文件=${await getBaiduOcrApiKey() ? '有' : '无'}`)

    if (!apiKey || !secretKey) {
      console.error('❌ 百度OCR API密钥未配置')
      console.error(`详细信息: apiKey=${apiKey}, secretKey=${secretKey}`)
      return res.status(400).json({
        success: false,
        error: '百度OCR API密钥未配置，请在设置中配置API密钥'
      })
    }

    // 获取访问令牌
    const accessToken = await getAccessToken(apiKey, secretKey)

    // 读取图像文件并转换为base64
    const imageBuffer = fs.readFileSync(imageFile.filepath)
    const base64Image = imageBuffer.toString('base64')

    console.log(`🔍 调用百度OCR高精度含位置版API识别图像`)

    // 调用百度OCR高精度含位置版API
    const ocrResponse = await fetch(`https://aip.baidubce.com/rest/2.0/ocr/v1/accurate?access_token=${accessToken}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded'
      },
      body: new URLSearchParams({
        image: base64Image,
        language_type: 'CHN_ENG', // 中英文混合
        detect_direction: 'true',  // 检测文字方向
        probability: 'true',       // 返回置信度
        paragraph: 'false',        // 不需要段落信息
        recognize_granularity: 'big' // 识别粒度：big（默认）、small
      })
    })

    if (!ocrResponse.ok) {
      const errorText = await ocrResponse.text()
      console.error(`❌ 百度OCR API请求失败: ${ocrResponse.status} - ${errorText}`)
      throw new Error(`百度OCR API请求失败: ${ocrResponse.status}`)
    }

    const ocrResult = await ocrResponse.json()
    console.log(`📊 百度OCR API响应:`, JSON.stringify(ocrResult, null, 2))

    if (ocrResult.error_code) {
      console.error(`❌ 百度OCR API错误: ${ocrResult.error_msg} (${ocrResult.error_code})`)
      throw new Error(`百度OCR API错误: ${ocrResult.error_msg} (${ocrResult.error_code})`)
    }

    // 转换结果格式
    const results: Array<{
      text: string
      confidence: number
      bbox: number[][]
    }> = []

    if (ocrResult.words_result && ocrResult.words_result.length > 0) {
      for (const item of ocrResult.words_result) {
        // 高精度含位置版包含location信息
        const location = item.location
        let bbox: number[][]

        if (location) {
          // 百度OCR返回的是矩形框的左上角坐标和宽高
          bbox = [
            [location.left, location.top],
            [location.left + location.width, location.top],
            [location.left + location.width, location.top + location.height],
            [location.left, location.top + location.height]
          ]
        } else {
          // 如果没有位置信息，使用默认bbox
          bbox = [
            [0, 0],
            [100, 0],
            [100, 20],
            [0, 20]
          ]
        }

        // 获取置信度
        let confidence = 0.95 // 高精度版默认更高的置信度
        if (item.probability) {
          confidence = typeof item.probability === 'object' 
            ? item.probability.average || 0.95
            : item.probability
        }

        results.push({
          text: item.words || '',
          confidence: confidence,
          bbox: bbox
        })
      }
    }

    console.log(`✅ 百度OCR高精度含位置版识别完成，共识别到 ${results.length} 个文本区域`)

    // 清理临时文件
    try {
      fs.unlinkSync(imageFile.filepath)
    } catch (error) {
      console.warn('清理临时文件失败:', error)
    }

    return res.status(200).json({
      success: true,
      results
    })

  } catch (error) {
    console.error('百度OCR高精度含位置版处理失败:', error)
    
    return res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : '未知错误'
    })
  }
}

/**
 * 获取访问令牌
 */
async function getAccessToken(apiKey: string, secretKey: string): Promise<string> {
  try {
    // 检查缓存的token是否还有效（提前5分钟刷新）
    const now = Date.now()
    if (cachedToken && tokenExpireTime > now + 5 * 60 * 1000) {
      return cachedToken
    }

    console.log('获取百度OCR访问令牌...')
    
    const response = await fetch('https://aip.baidubce.com/oauth/2.0/token', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded'
      },
      body: new URLSearchParams({
        grant_type: 'client_credentials',
        client_id: apiKey,
        client_secret: secretKey
      })
    })

    if (!response.ok) {
      throw new Error(`获取访问令牌失败: ${response.status}`)
    }

    const data = await response.json()
    
    if (data.error) {
      throw new Error(`百度API错误: ${data.error_description || data.error}`)
    }

    cachedToken = data.access_token
    tokenExpireTime = now + (data.expires_in * 1000)
    
    console.log('✅ 百度OCR访问令牌获取成功')
    return cachedToken

  } catch (error) {
    console.error('获取百度OCR访问令牌失败:', error)
    throw error
  }
}

/**
 * 获取百度OCR API密钥
 */
async function getBaiduOcrApiKey(): Promise<string | null> {
  // 优先从环境变量获取
  if (process.env.BAIDU_OCR_API_KEY) {
    return process.env.BAIDU_OCR_API_KEY
  }

  // 尝试从Docker配置获取
  try {
    const { DockerConfigManager } = await import('@/lib/docker-config-manager')
    return DockerConfigManager.getBaiduOcrApiKey()
  } catch (error) {
    console.warn('无法加载Docker配置管理器:', error)
    return null
  }
}

/**
 * 获取百度OCR Secret Key
 */
async function getBaiduOcrSecretKey(): Promise<string | null> {
  // 优先从环境变量获取
  if (process.env.BAIDU_OCR_SECRET_KEY) {
    return process.env.BAIDU_OCR_SECRET_KEY
  }

  // 尝试从Docker配置获取
  try {
    const { DockerConfigManager } = await import('@/lib/docker-config-manager')
    return DockerConfigManager.getBaiduOcrSecretKey()
  } catch (error) {
    console.warn('无法加载Docker配置管理器:', error)
    return null
  }
}
