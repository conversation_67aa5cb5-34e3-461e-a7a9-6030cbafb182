// 后端OCR处理器 - 通过API调用后端的PaddleOCR服务
export interface OCRResult {
  text: string
  confidence: number
  bbox: number[][]
}

export class BackendOCRProcessor {
  private isInitialized = false
  private baseUrl: string

  constructor() {
    this.baseUrl = typeof window !== 'undefined' ? window.location.origin : 'http://localhost:3001'
  }

  async initialize(): Promise<void> {
    try {
      console.log('正在初始化后端OCR处理器...')
      
      // 测试后端API是否可用
      const response = await fetch(`${this.baseUrl}/api/ocr/analyze-frame`, {
        method: 'OPTIONS'
      })
      
      // 即使OPTIONS返回405也是正常的，说明API端点存在
      if (response.status === 405 || response.status === 200) {
        this.isInitialized = true
        console.log('✅ 后端OCR处理器初始化完成')
      } else {
        throw new Error('后端API不可用')
      }
    } catch (error) {
      console.error('后端OCR处理器初始化失败:', error)
      throw new Error('无法连接到后端OCR服务')
    }
  }

  async processImage(imageData: ImageData): Promise<OCRResult[]> {
    if (!this.isInitialized) {
      throw new Error('处理器未初始化')
    }

    try {
      // 将ImageData转换为Blob
      const canvas = document.createElement('canvas')
      const ctx = canvas.getContext('2d')!
      
      canvas.width = imageData.width
      canvas.height = imageData.height
      ctx.putImageData(imageData, 0, 0)
      
      // 转换为Blob
      const blob = await new Promise<Blob>((resolve) => {
        canvas.toBlob((blob) => {
          resolve(blob!)
        }, 'image/png')
      })

      // 创建FormData
      const formData = new FormData()
      formData.append('image', blob, 'frame.png')
      formData.append('mode', 'ocr')

      // 发送到后端API
      const response = await fetch(`${this.baseUrl}/api/ocr/analyze-frame`, {
        method: 'POST',
        body: formData
      })

      if (!response.ok) {
        throw new Error(`API请求失败: ${response.status}`)
      }

      const result = await response.json()
      
      if (!result.success) {
        throw new Error(result.error || 'OCR处理失败')
      }

      return result.results || []
    } catch (error) {
      console.error('后端OCR处理失败:', error)
      throw error
    }
  }

  async detectAnchors(imageData: ImageData): Promise<{
    centerX: number
    centerY: number
    width: number
    height: number
    confidence: number
  }[]> {
    if (!this.isInitialized) {
      throw new Error('处理器未初始化')
    }

    try {
      // 将ImageData转换为Blob
      const canvas = document.createElement('canvas')
      const ctx = canvas.getContext('2d')!
      
      canvas.width = imageData.width
      canvas.height = imageData.height
      ctx.putImageData(imageData, 0, 0)
      
      // 转换为Blob
      const blob = await new Promise<Blob>((resolve) => {
        canvas.toBlob((blob) => {
          resolve(blob!)
        }, 'image/png')
      })

      // 创建FormData
      const formData = new FormData()
      formData.append('image', blob, 'frame.png')
      formData.append('mode', 'anchors')

      // 发送到后端API
      const response = await fetch(`${this.baseUrl}/api/ocr/analyze-frame`, {
        method: 'POST',
        body: formData
      })

      if (!response.ok) {
        throw new Error(`API请求失败: ${response.status}`)
      }

      const result = await response.json()
      
      if (!result.success) {
        throw new Error(result.error || '锚点检测失败')
      }

      return result.anchors || []
    } catch (error) {
      console.error('后端锚点检测失败:', error)
      throw error
    }
  }

  reset(): void {
    // 后端处理器无需重置状态
  }

  dispose(): void {
    this.isInitialized = false
    console.log('后端OCR处理器已释放')
  }

  getStatus(): {
    isInitialized: boolean
    type: string
    version: string
  } {
    return {
      isInitialized: this.isInitialized,
      type: 'Backend OCR Processor',
      version: '1.0.0'
    }
  }
}
