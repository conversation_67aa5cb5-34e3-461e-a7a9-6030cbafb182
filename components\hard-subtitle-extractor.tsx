"use client"

import React, { useState, useEffect, useRef, use<PERSON><PERSON>back } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Progress } from '@/components/ui/progress'
import { Badge } from '@/components/ui/badge'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Separator } from '@/components/ui/separator'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Slider } from '@/components/ui/slider'
import { Checkbox } from '@/components/ui/checkbox'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Textarea } from '@/components/ui/textarea'
import {
  Upload,
  Play,
  Pause,
  Download,
  Settings,
  FileText,
  Image as ImageIcon,
  Trash2,
  Plus,
  Eye,
  EyeOff,
  RotateCcw,
  Loader2,
  CheckCircle2,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  Target
} from 'lucide-react'
import { toast } from 'sonner'
import { SimpleOCRProcessor } from '@/utils/simple-ocr-processor'
import { BackendOCRProcessor } from '@/utils/backend-ocr-processor'
import { BaiduOCRProcessor, type OCRResult } from '@/utils/baidu-ocr-processor'
import { SmartFrameExtractor, type FrameData } from '@/utils/smart-frame-extractor'
import { SubtitleMosaicProcessor, type MappedSubtitle } from '@/utils/subtitle-mosaic-processor'

// 字幕锚点接口
interface SubtitleAnchor {
  id: string
  centerX: number
  centerY: number
  width: number
  height: number
  language: string
  isPrimary: boolean
  color: string
  // 动态属性
  originalWidth?: number  // 原始宽度
  originalHeight?: number // 原始高度
  minWidth?: number      // 最小宽度
  maxWidth?: number      // 最大宽度
  expandRatio?: number   // 扩展比例
  lastDetectedBounds?: { // 上次检测到的边界
    minX: number
    maxX: number
    minY: number
    maxY: number
  }
}

// 字幕结果接口
interface SubtitleResult {
  id: string
  startTime: number
  endTime: number
  text: string
  confidence: number
  anchorId: string
}

// 进度状态接口
interface ExtractionProgress {
  isRunning: boolean
  currentFrame: number
  totalFrames: number
  currentTime: number
  totalTime: number
  speed: number
  stage: 'analyzing' | 'extracting' | 'completed' | 'error'
  current?: number // 当前进度百分比
  totalSubtitles?: number // 已提取的字幕数量
}

// 语言选项
const LANGUAGE_OPTIONS = [
  { value: 'zh', label: '中文' },
  { value: 'en', label: '英语' },
  { value: 'ja', label: '日语' },
  { value: 'ko', label: '韩语' }
]

// 锚点颜色
const ANCHOR_COLORS = [
  '#FF0000', // Red
  '#0000FF', // Blue
  '#008080', // Teal
  '#800080', // Purple
  '#FFFF00', // Yellow
  '#FFA500', // Orange
  '#008000', // Green
  '#FFC0CB', // Pink
  '#808080', // Gray
  '#A52A2A'  // Brown
]

// 时间格式化函数
const formatTime = (seconds: number): string => {
  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  const secs = Math.floor(seconds % 60)
  const ms = Math.floor((seconds % 1) * 1000)
  
  if (hours > 0) {
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}.${ms.toString().padStart(3, '0')}`
  }
  return `${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}.${ms.toString().padStart(3, '0')}`
}

// SRT格式导出函数
const exportToSRT = (subtitles: SubtitleResult[]): string => {
  return subtitles.map((subtitle, index) => {
    const startTime = formatTime(subtitle.startTime)
    const endTime = formatTime(subtitle.endTime)
    return `${index + 1}\n${startTime} --> ${endTime}\n${subtitle.text}\n`
  }).join('\n')
}

export function HardSubtitleExtractor() {
  // 状态管理
  const [videoFile, setVideoFile] = useState<File | null>(null)
  const [videoUrl, setVideoUrl] = useState<string>('')
  const [currentTime, setCurrentTime] = useState<number>(0)
  const [duration, setDuration] = useState<number>(0)
  const [isPlaying, setIsPlaying] = useState<boolean>(false)
  const [anchors, setAnchors] = useState<SubtitleAnchor[]>([])
  // 动态调整后的锚点（用于实时显示）
  const [dynamicAnchors, setDynamicAnchors] = useState<SubtitleAnchor[]>([])
  const [lastDynamicUpdateTime, setLastDynamicUpdateTime] = useState<number>(0)
  const [subtitles, setSubtitles] = useState<SubtitleResult[]>([])
  const [selectedSubtitle, setSelectedSubtitle] = useState<string | null>(null)
  const [progress, setProgress] = useState<ExtractionProgress>({
    isRunning: false,
    currentFrame: 0,
    totalFrames: 0,
    currentTime: 0,
    totalTime: 0,
    speed: 1,
    stage: 'analyzing'
  })

  // 停止提取标志
  const [shouldStop, setShouldStop] = useState<boolean>(false)

  // 停止提取函数
  const handleStopExtraction = useCallback(() => {
    setShouldStop(true)
    setIsRealtimeMode(false) // 同时停止实时识别模式
    setProgress(prev => ({
      ...prev,
      isRunning: false,
      stage: 'completed'
    }))
    toast.info('正在停止字幕提取...')
    console.log('🛑 用户请求停止字幕提取')
  }, [])

  // OCR处理器
  const [ocrProcessor, setOcrProcessor] = useState<BaiduOCRProcessor | SimpleOCRProcessor | BackendOCRProcessor | null>(null)
  const [isModelLoading, setIsModelLoading] = useState<boolean>(false)
  const [processorType, setProcessorType] = useState<'baidu' | 'backend' | 'simple'>('baidu')
  
  // 设置状态
  const [fps, setFps] = useState<number>(10)
  const [minSubtitleDuration, setMinSubtitleDuration] = useState<number>(500)
  const [autoAnalyze, setAutoAnalyze] = useState<boolean>(false)
  const [showAnchors, setShowAnchors] = useState<boolean>(true)
  
  // 引用
  const videoRef = useRef<HTMLVideoElement>(null)
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const fileInputRef = useRef<HTMLInputElement>(null)

  // 实时显示状态（不再进行OCR，只显示锚点）
  const [isRealtimeMode, setIsRealtimeMode] = useState<boolean>(false)

  // 手动标记状态
  const [isManualMode, setIsManualMode] = useState<boolean>(false)
  const [isDrawing, setIsDrawing] = useState<boolean>(false)
  const [drawStart, setDrawStart] = useState<{ x: number; y: number } | null>(null)
  const [currentDrawRect, setCurrentDrawRect] = useState<{ x: number; y: number; width: number; height: number } | null>(null)

  // 调试模式
  const [debugMode, setDebugMode] = useState<boolean>(false)
  const [debugImages, setDebugImages] = useState<Array<{
    anchorId: string
    croppedImage: string
    ocrResults: OCRResult[]
  }>>([])

  // 测试单个锚点
  const [testingAnchor, setTestingAnchor] = useState<boolean>(false)

  // 性能监控状态
  const [performanceStats, setPerformanceStats] = useState<{
    avgProcessingTime: number
    totalProcessedFrames: number
    speedupRatio: number
    skippedFrames: number
    cacheHitRate: number
  }>({
    avgProcessingTime: 0,
    totalProcessedFrames: 0,
    speedupRatio: 0,
    skippedFrames: 0,
    cacheHitRate: 0
  })

  // 智能提取配置
  const [smartExtractionConfig, setSmartExtractionConfig] = useState({
    enabled: true,
    batchSize: 10,
    skipSimilarFrames: true,
    similarityThreshold: 0.95,
    adaptiveSampling: true,
    maxConcurrentBatches: 3
  })

  // 智能帧提取器
  const [frameExtractor, setFrameExtractor] = useState<SmartFrameExtractor | null>(null)

  // 拼图处理器
  const [mosaicProcessor] = useState(() => new SubtitleMosaicProcessor())

  // API限制状态
  const [apiLimitReached, setApiLimitReached] = useState<boolean>(false)

  // 初始化OCR处理器
  useEffect(() => {
    const initializeOCR = async () => {
      if (ocrProcessor) return

      setIsModelLoading(true)
      setApiLimitReached(false) // 重置API限制状态
      console.log('🚀 开始初始化OCR处理器...')

      // 尝试按优先级初始化处理器
      const processors = [
        {
          name: '百度OCR',
          type: 'baidu' as const,
          create: () => new BaiduOCRProcessor(),
          description: '百度OCR处理器连接成功'
        },
        {
          name: '后端OCR',
          type: 'backend' as const,
          create: () => new BackendOCRProcessor(),
          description: '后端OCR处理器连接成功'
        },
        {
          name: '简化OCR',
          type: 'simple' as const,
          create: () => new SimpleOCRProcessor(),
          description: '简化OCR处理器加载完成（演示模式）'
        }
      ]

      for (const processorConfig of processors) {
        try {
          console.log(`🔄 尝试初始化${processorConfig.name}处理器...`)
          const processor = processorConfig.create()

          // 添加超时控制
          const initPromise = processor.initialize()
          const timeoutPromise = new Promise((_, reject) => {
            setTimeout(() => reject(new Error('初始化超时')), 10000)
          })

          await Promise.race([initPromise, timeoutPromise])

          setOcrProcessor(processor)
          setProcessorType(processorConfig.type)

          toast.success(processorConfig.description)
          console.log(`✅ ${processorConfig.name}处理器初始化成功`)
          break
        } catch (error) {
          console.warn(`❌ ${processorConfig.name}处理器初始化失败:`, error)

          if (processorConfig.type === 'simple') {
            // 如果连简化处理器都失败了，显示错误
            toast.error('所有OCR处理器初始化失败', {
              duration: 8000,
              description: '请检查浏览器控制台获取详细错误信息'
            })
          }
        }
      }

      setIsModelLoading(false)
      console.log('🏁 OCR处理器初始化完成')
    }

    initializeOCR()

    // 清理函数
    return () => {
      if (ocrProcessor) {
        console.log('🧹 清理OCR处理器')
        ocrProcessor.dispose()
      }
    }
  }, [])

  // 处理文件上传
  const handleFileUpload = useCallback((file: File) => {
    if (!file.type.startsWith('video/')) {
      toast.error('请选择视频文件')
      return
    }

    setVideoFile(file)
    const url = URL.createObjectURL(file)
    setVideoUrl(url)
    setAnchors([])
    setSubtitles([])
    setSelectedSubtitle(null)

    // 重置OCR处理器状态（如果有reset方法）
    // PP-OCRv5处理器不需要重置状态

    toast.success('视频文件已加载')

    if (autoAnalyze) {
      // 自动分析字幕锚点
      setTimeout(() => {
        handleAnalyzeAnchors()
      }, 1000)
    }
  }, [autoAnalyze])

  // 处理拖拽上传
  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    const files = Array.from(e.dataTransfer.files)
    if (files.length > 0) {
      handleFileUpload(files[0])
    }
  }, [handleFileUpload])

  // 处理视频加载
  const handleVideoLoad = useCallback(() => {
    if (videoRef.current) {
      setDuration(videoRef.current.duration)
      setCurrentTime(0)
    }
  }, [])

  // 临时移除updateDynamicAnchors，稍后在detectTextBounds之后重新定义

  // 处理时间更新
  const handleTimeUpdate = useCallback(() => {
    if (videoRef.current) {
      const newTime = videoRef.current.currentTime
      setCurrentTime(newTime)

      // 每0.5秒更新一次动态锚点（避免过于频繁）
      const now = Date.now()
      if (now - lastDynamicUpdateTime > 500 && anchors.length > 0) {
        // 异步更新动态锚点
        (async () => {
          try {
            const imageData = await extractFrameImageData(newTime)
            const updatedAnchors = anchors.map(anchor => {
              const bounds = detectTextBounds(imageData, anchor)
              return {
                ...anchor,
                centerX: Math.round((bounds.left + bounds.right) / 2),
                centerY: Math.round((bounds.top + bounds.bottom) / 2),
                width: Math.round(bounds.right - bounds.left),
                height: Math.round(bounds.bottom - bounds.top)
              }
            })
            setDynamicAnchors(updatedAnchors)
          } catch (error) {
            console.warn('动态锚点更新失败:', error)
          }
        })()
        setLastDynamicUpdateTime(now)
      }
    }
  }, [anchors, lastDynamicUpdateTime])

  // 播放/暂停控制
  const togglePlayPause = useCallback(() => {
    if (videoRef.current) {
      if (isPlaying) {
        videoRef.current.pause()
      } else {
        videoRef.current.play()
      }
      setIsPlaying(!isPlaying)
    }
  }, [isPlaying])

  // 跳转到指定时间
  const seekTo = useCallback((time: number) => {
    if (videoRef.current) {
      videoRef.current.currentTime = time
      setCurrentTime(time)
    }
  }, [])

  // 裁剪图像区域
  const cropImageData = (imageData: ImageData, x: number, y: number, width: number, height: number): ImageData => {
    const canvas = document.createElement('canvas')
    const ctx = canvas.getContext('2d')!

    canvas.width = width
    canvas.height = height

    // 创建临时画布
    const tempCanvas = document.createElement('canvas')
    const tempCtx = tempCanvas.getContext('2d')!
    tempCanvas.width = imageData.width
    tempCanvas.height = imageData.height
    tempCtx.putImageData(imageData, 0, 0)

    // 裁剪区域
    ctx.drawImage(tempCanvas, x, y, width, height, 0, 0, width, height)

    return ctx.getImageData(0, 0, width, height)
  }

  // 动态检测字幕文本边界
  const detectTextBounds = (imageData: ImageData, anchor: SubtitleAnchor): {
    left: number
    right: number
    top: number
    bottom: number
  } => {
    try {
      const { width, height, data } = imageData

      // 基于锚点的搜索区域（扩大搜索范围以支持动态宽度）
      const searchLeft = Math.max(0, Math.floor(anchor.centerX - anchor.width * 1.5))
      const searchRight = Math.min(width, Math.floor(anchor.centerX + anchor.width * 1.5))
      const searchTop = Math.max(0, Math.floor(anchor.centerY - anchor.height / 2))
      const searchBottom = Math.min(height, Math.floor(anchor.centerY + anchor.height / 2))

      let textLeft = searchRight
      let textRight = searchLeft
      let textTop = searchBottom
      let textBottom = searchTop

      // 扫描搜索区域，寻找文本像素
      for (let y = searchTop; y < searchBottom; y++) {
        for (let x = searchLeft; x < searchRight; x++) {
          const index = (y * width + x) * 4

          // 确保索引在有效范围内
          if (index + 2 >= data.length) continue

          const r = data[index]
          const g = data[index + 1]
          const b = data[index + 2]

          // 检测高对比度像素（可能是文字）
          const brightness = (r + g + b) / 3
          const isTextPixel = brightness < 80 || brightness > 180

          if (isTextPixel) {
            textLeft = Math.min(textLeft, x)
            textRight = Math.max(textRight, x)
            textTop = Math.min(textTop, y)
            textBottom = Math.max(textBottom, y)
          }
        }
      }

      // 如果没有检测到文本，使用原始锚点
      if (textLeft >= textRight || textTop >= textBottom) {
        console.log(`📏 锚点 ${anchor.id}: 未检测到文本，使用原始边界`)
        return {
          left: Math.max(0, Math.floor(anchor.centerX - anchor.width / 2)),
          right: Math.min(width, Math.floor(anchor.centerX + anchor.width / 2)),
          top: Math.max(0, Math.floor(anchor.centerY - anchor.height / 2)),
          bottom: Math.min(height, Math.floor(anchor.centerY + anchor.height / 2))
        }
      }

      // 添加边距，但保持高度相对固定
      const horizontalMargin = 15
      const verticalMargin = 5

      const bounds = {
        left: Math.max(0, textLeft - horizontalMargin),
        right: Math.min(width, textRight + horizontalMargin),
        top: Math.max(0, Math.min(textTop - verticalMargin, anchor.centerY - anchor.height / 2)),
        bottom: Math.min(height, Math.max(textBottom + verticalMargin, anchor.centerY + anchor.height / 2))
      }

      console.log(`📏 锚点 ${anchor.id}: 动态边界检测完成`, {
        原始: { x: anchor.centerX, y: anchor.centerY, w: anchor.width, h: anchor.height },
        检测: bounds,
        宽度变化: `${anchor.width} → ${bounds.right - bounds.left}`
      })

      return bounds
    } catch (error) {
      console.error('动态边界检测失败:', error)
      // 发生错误时返回原始锚点边界
      return {
        left: Math.max(0, Math.floor(anchor.centerX - anchor.width / 2)),
        right: Math.min(imageData.width, Math.floor(anchor.centerX + anchor.width / 2)),
        top: Math.max(0, Math.floor(anchor.centerY - anchor.height / 2)),
        bottom: Math.min(imageData.height, Math.floor(anchor.centerY + anchor.height / 2))
      }
    }
  }

  // 根据锚点裁剪图像区域（支持动态调整）
  const cropAnchorRegion = (imageData: ImageData, anchor: SubtitleAnchor): {
    croppedImage: ImageData
    offsetX: number
    offsetY: number
    dynamicBounds?: any
  } => {
    // 动态检测文本边界
    const bounds = detectTextBounds(imageData, anchor)

    const width = bounds.right - bounds.left
    const height = bounds.bottom - bounds.top

    console.log(`📏 动态锚点调整 ${anchor.id}:`, {
      原始: { x: anchor.centerX, y: anchor.centerY, w: anchor.width, h: anchor.height },
      检测到: { left: bounds.left, right: bounds.right, top: bounds.top, bottom: bounds.bottom },
      裁剪: { width, height }
    })

    const croppedImage = cropImageData(imageData, bounds.left, bounds.top, width, height)

    return {
      croppedImage,
      offsetX: bounds.left,
      offsetY: bounds.top,
      dynamicBounds: bounds
    }
  }

  // 从视频帧中提取图像数据
  const extractFrameImageData = useCallback((time: number): Promise<ImageData> => {
    return new Promise((resolve, reject) => {
      if (!videoRef.current || !canvasRef.current) {
        reject(new Error('视频或画布未准备就绪'))
        return
      }

      const video = videoRef.current
      const canvas = canvasRef.current
      const ctx = canvas.getContext('2d')!

      // 设置画布尺寸
      canvas.width = video.videoWidth
      canvas.height = video.videoHeight

      // 跳转到指定时间
      video.currentTime = time

      const handleSeeked = () => {
        // 绘制当前帧到画布
        ctx.drawImage(video, 0, 0, canvas.width, canvas.height)

        // 获取图像数据
        const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height)

        video.removeEventListener('seeked', handleSeeked)
        resolve(imageData)
      }

      video.addEventListener('seeked', handleSeeked)
    })
  }, [])

  // 分析字幕锚点
  const handleAnalyzeAnchors = useCallback(async () => {
    if (!videoFile || !ocrProcessor) {
      toast.error('请先选择视频文件并等待模型加载完成')
      return
    }

    setProgress(prev => ({
      ...prev,
      isRunning: true,
      stage: 'analyzing',
      currentFrame: 0,
      totalFrames: Math.floor(duration * 2), // 分析前2秒
      totalTime: 2
    }))

    try {
      const detectedAnchors: SubtitleAnchor[] = []
      const sampleTimes = [0.5, 1.0, 1.5] // 采样时间点

      for (let i = 0; i < sampleTimes.length; i++) {
        const time = sampleTimes[i]

        setProgress(prev => ({
          ...prev,
          currentFrame: i + 1,
          currentTime: time,
          speed: 1
        }))

        try {
          // 提取视频帧
          const imageData = await extractFrameImageData(time)

          // 使用OCR处理器进行锚点检测
          if ('detectAnchors' in ocrProcessor) {
            // 使用支持锚点检测的处理器
            const anchorResults = await ocrProcessor.detectAnchors(imageData)

            anchorResults.forEach((result, index) => {
              if (result.confidence > 0.5) {
                const anchorId = `anchor_${Date.now()}_${index}`

                detectedAnchors.push({
                  id: anchorId,
                  centerX: Math.round(result.centerX),
                  centerY: Math.round(result.centerY),
                  width: Math.round(result.width),
                  height: Math.round(result.height),
                  language: 'zh',
                  isPrimary: index === 0,
                  color: ANCHOR_COLORS[detectedAnchors.length % ANCHOR_COLORS.length]
                })
              }
            })
          } else {
            // 使用OCR识别进行锚点检测
            const ocrResults = await ocrProcessor.processImage(imageData)

            // 优化的锚点检测算法
            const optimizedAnchors = optimizeAnchorDetection(ocrResults, imageData)

            optimizedAnchors.forEach((anchor, index) => {
              detectedAnchors.push({
                id: `anchor_${Date.now()}_${index}`,
                centerX: Math.round(anchor.centerX),
                centerY: Math.round(anchor.centerY),
                width: Math.round(anchor.width),
                height: Math.round(anchor.height),
                language: 'zh',
                isPrimary: index === 0,
                color: ANCHOR_COLORS[detectedAnchors.length % ANCHOR_COLORS.length]
              })
            })
          }
        } catch (error) {
          console.warn(`分析时间点 ${time}s 失败:`, error)
        }
      }

      // 去重和合并相似的锚点
      const mergedAnchors = mergeSimilarAnchors(detectedAnchors)

      setAnchors(mergedAnchors)
      setProgress(prev => ({ ...prev, isRunning: false, stage: 'completed' }))

      if (mergedAnchors.length > 0) {
        toast.success(`字幕锚点分析完成，发现 ${mergedAnchors.length} 个锚点`)
      } else {
        toast.warning('未检测到字幕区域，请手动添加锚点')
      }
    } catch (error) {
      console.error('字幕锚点分析失败:', error)
      setProgress(prev => ({ ...prev, isRunning: false, stage: 'error' }))
      toast.error('字幕锚点分析失败')
    }
  }, [videoFile, ocrProcessor, duration, extractFrameImageData])

  // 合并相似的锚点
  const mergeSimilarAnchors = (anchors: SubtitleAnchor[]): SubtitleAnchor[] => {
    const merged: SubtitleAnchor[] = []
    const threshold = 50 // 距离阈值

    anchors.forEach(anchor => {
      const similar = merged.find(m =>
        Math.abs(m.centerX - anchor.centerX) < threshold &&
        Math.abs(m.centerY - anchor.centerY) < threshold
      )

      if (!similar) {
        merged.push(anchor)
      }
    })

    return merged
  }

  // 开始字幕提取
  const handleExtractSubtitles = useCallback(async () => {
    if (!videoFile || !ocrProcessor || anchors.length === 0) {
      toast.error('请先选择视频文件、等待模型加载并分析锚点')
      return
    }

    const primaryAnchors = anchors.filter(a => a.isPrimary)
    if (primaryAnchors.length === 0) {
      toast.error('请至少设置一个主字幕锚点')
      return
    }

    // 重置停止标志
    setShouldStop(false)

    setProgress(prev => ({
      ...prev,
      isRunning: true,
      stage: 'extracting',
      currentFrame: 0,
      totalFrames: Math.floor(duration), // 按秒计算总数
      totalTime: duration
    }))

    try {
      const extractedSubtitles: SubtitleResult[] = []
      const timeInterval = 1.0 // 改为逐秒提取
      let currentSubtitle: SubtitleResult | null = null

      console.log(`🚀 开始逐秒字幕提取，总时长: ${duration.toFixed(1)}秒`)

      for (let time = 0; time < duration; time += timeInterval) {
        // 检查是否需要停止
        if (shouldStop) {
          console.log(`🛑 在 ${time.toFixed(1)}s 处停止提取`)
          break
        }

        const frameStartTime = performance.now()

        setProgress(prev => ({
          ...prev,
          currentFrame: Math.floor(time),
          totalFrames: Math.floor(duration),
          currentTime: time,
          speed: 1 // 1秒/秒的处理速度
        }))

        try {
          // 提取视频帧
          const imageData = await extractFrameImageData(time)

          // 对每个主锚点进行优化的OCR识别
          let allTexts: string[] = []

          for (const anchor of primaryAnchors) {
            try {
              // 使用优化的锚点处理（智能动态调整）
              const { results, adjustedAnchor } = await processAnchorOCR(imageData, anchor)

              // 智能动态调整锚点（只在有显著变化时调整）
              const anchorIndex = primaryAnchors.findIndex(a => a.id === anchor.id)
              if (anchorIndex !== -1) {
                // 检查是否需要更新锚点
                const shouldUpdateAnchor =
                  Math.abs(adjustedAnchor.centerX - anchor.centerX) > 10 ||
                  Math.abs(adjustedAnchor.centerY - anchor.centerY) > 10 ||
                  Math.abs(adjustedAnchor.width - anchor.width) > 20 ||
                  Math.abs(adjustedAnchor.height - anchor.height) > 10

                if (shouldUpdateAnchor) {
                  primaryAnchors[anchorIndex] = adjustedAnchor

                  // 更新React状态中的锚点（节流更新，避免过于频繁）
                  setAnchors(prevAnchors =>
                    prevAnchors.map(a =>
                      a.id === anchor.id ? adjustedAnchor : a
                    )
                  )

                  console.log(`🔄 提取中动态调整锚点 ${anchor.id}:`, {
                    原始: { x: Math.round(anchor.centerX), y: Math.round(anchor.centerY), w: Math.round(anchor.width), h: Math.round(anchor.height) },
                    调整后: { x: Math.round(adjustedAnchor.centerX), y: Math.round(adjustedAnchor.centerY), w: Math.round(adjustedAnchor.width), h: Math.round(adjustedAnchor.height) }
                  })
                }
              }

              // 收集文本
              const anchorTexts = results
                .filter(result => result.confidence > 0.5)
                .map(result => result.text.trim())
                .filter(text => text.length > 0)

              allTexts.push(...anchorTexts)
            } catch (error) {
              console.warn(`处理锚点 ${anchor.id} 失败:`, error)

              // 降级到简单裁剪方式
              try {
                const { croppedImage } = cropAnchorRegion(imageData, anchor)
                const ocrResults = await ocrProcessor.processImage(croppedImage)

                const anchorTexts = ocrResults
                  .filter(result => result.confidence > 0.5)
                  .map(result => result.text.trim())
                  .filter(text => text.length > 0)

                allTexts.push(...anchorTexts)
              } catch (fallbackError) {
                console.warn(`锚点 ${anchor.id} 降级处理也失败:`, fallbackError)
              }
            }
          }

          // 合并所有文本
          const currentText = allTexts.join(' ').trim()

          if (currentText) {
            // 检查是否与当前字幕相同或相似
            if (currentSubtitle && isSubtitleSimilar(currentSubtitle.text, currentText, 0.85)) {
              // 相同或相似字幕，延长结束时间
              currentSubtitle.endTime = time + timeInterval
              if (currentSubtitle.text !== currentText) {
                console.log(`⏱️ 相似字幕延长: "${currentSubtitle.text}" -> "${currentText}" 至 ${(time + timeInterval).toFixed(1)}s`)
                // 更新字幕文本为更完整的版本
                currentSubtitle.text = currentText
              } else {
                console.log(`⏱️ 延长字幕: "${currentText}" 至 ${(time + timeInterval).toFixed(1)}s`)
              }

              // 实时更新UI - 显示延长后的字幕
              const currentSubtitles = [...extractedSubtitles, currentSubtitle]
              setSubtitles(currentSubtitles)
            } else {
              // 新字幕或不同字幕
              if (currentSubtitle) {
                // 完成上一个字幕
                if (currentSubtitle.endTime - currentSubtitle.startTime >= minSubtitleDuration / 1000) {
                  extractedSubtitles.push(currentSubtitle)
                  console.log(`✅ 完成字幕: "${currentSubtitle.text}" (${currentSubtitle.startTime.toFixed(1)}s - ${currentSubtitle.endTime.toFixed(1)}s)`)

                  // 实时更新UI - 立即显示新完成的字幕
                  setSubtitles([...extractedSubtitles])
                }
              }

              // 开始新字幕
              currentSubtitle = {
                id: `subtitle_${extractedSubtitles.length}`,
                startTime: time,
                endTime: time + timeInterval,
                text: currentText,
                confidence: 0.9,
                anchorId: primaryAnchors[0].id
              }
              console.log(`🆕 新字幕开始: "${currentText}" 在 ${time.toFixed(1)}s`)

              // 实时显示正在进行的字幕（包括当前正在识别的字幕）
              const currentSubtitles = [...extractedSubtitles, currentSubtitle]
              setSubtitles(currentSubtitles)
            }
          } else {
            // 当前时间点没有字幕
            if (currentSubtitle) {
              // 完成当前字幕
              if (currentSubtitle.endTime - currentSubtitle.startTime >= minSubtitleDuration / 1000) {
                extractedSubtitles.push(currentSubtitle)
                console.log(`✅ 字幕结束: "${currentSubtitle.text}" (${currentSubtitle.startTime.toFixed(1)}s - ${currentSubtitle.endTime.toFixed(1)}s)`)

                // 实时更新UI
                setSubtitles([...extractedSubtitles])
              }
              currentSubtitle = null
            }
          }
        } catch (error) {
          console.warn(`提取时间点 ${time.toFixed(2)}s 失败:`, error)
        }

        // 更新性能统计
        const frameEndTime = performance.now()
        const processingTime = frameEndTime - frameStartTime

        setPerformanceStats(prev => {
          const newTotalFrames = prev.totalProcessedFrames + 1
          const newAvgTime = (prev.avgProcessingTime * prev.totalProcessedFrames + processingTime) / newTotalFrames
          const speedupRatio = newAvgTime > 0 ? (1000 / fps) / newAvgTime : 0

          return {
            avgProcessingTime: newAvgTime,
            totalProcessedFrames: newTotalFrames,
            speedupRatio: speedupRatio
          }
        })

        // 更新进度
        const progressPercent = Math.round((time / duration) * 100)
        setProgress(prev => ({
          ...prev,
          current: progressPercent,
          stage: 'extracting',
          currentTime: time,
          totalSubtitles: extractedSubtitles.length
        }))

        // 每处理几秒更新一次UI（减少频率）
        if (Math.floor(time) % 5 === 0) {
          await new Promise(resolve => setTimeout(resolve, 10))
        }
      }

      // 处理最后一个字幕
      if (currentSubtitle && currentSubtitle.endTime - currentSubtitle.startTime >= minSubtitleDuration / 1000) {
        extractedSubtitles.push(currentSubtitle)
        console.log(`✅ 最后字幕: "${currentSubtitle.text}" (${currentSubtitle.startTime.toFixed(1)}s - ${currentSubtitle.endTime.toFixed(1)}s)`)
      }

      setSubtitles(extractedSubtitles)
      setProgress(prev => ({ ...prev, isRunning: false, stage: 'completed' }))

      if (shouldStop) {
        toast.info(`字幕提取已停止，已提取 ${extractedSubtitles.length} 条字幕`)
        console.log(`🛑 字幕提取已停止，共提取 ${extractedSubtitles.length} 条字幕`)
      } else if (extractedSubtitles.length > 0) {
        toast.success(`字幕提取完成，共提取 ${extractedSubtitles.length} 条字幕`)
        console.log(`✅ 字幕提取完成，共提取 ${extractedSubtitles.length} 条字幕`)
      } else {
        toast.warning('未提取到字幕内容')
      }
    } catch (error) {
      console.error('字幕提取失败:', error)
      setProgress(prev => ({ ...prev, isRunning: false, stage: 'error' }))
      toast.error('字幕提取失败')
    } finally {
      // 重置停止标志
      setShouldStop(false)
    }
  }, [videoFile, ocrProcessor, anchors, duration, fps, minSubtitleDuration, extractFrameImageData, shouldStop])

  // 在canvas上绘制锚点和OCR结果
  const drawOverlay = useCallback(() => {
    if (!canvasRef.current || !videoRef.current) return

    const canvas = canvasRef.current
    const video = videoRef.current
    const ctx = canvas.getContext('2d')!

    // 设置canvas尺寸匹配视频
    canvas.width = video.videoWidth
    canvas.height = video.videoHeight

    // 清除之前的绘制
    ctx.clearRect(0, 0, canvas.width, canvas.height)

    // 绘制锚点（优先使用动态锚点）
    const anchorsToRender = dynamicAnchors.length > 0 ? dynamicAnchors : anchors

    anchorsToRender.forEach((anchor, index) => {
      const x = anchor.centerX - anchor.width / 2
      const y = anchor.centerY - anchor.height / 2

      // 绘制锚点边框
      ctx.strokeStyle = anchor.color
      ctx.lineWidth = 3
      ctx.setLineDash(anchor.isPrimary ? [] : [5, 5])
      ctx.strokeRect(x, y, anchor.width, anchor.height)

      // 绘制锚点标签
      ctx.fillStyle = anchor.color
      ctx.font = '16px Arial'
      const isDynamic = dynamicAnchors.length > 0
      const labelText = `锚点${index + 1} ${isDynamic ? '(动态)' : ''} (${anchor.language})`
      ctx.fillText(labelText, x, y - 5)

      // 如果是动态锚点，显示尺寸变化信息
      if (isDynamic && anchors[index]) {
        const originalAnchor = anchors[index]
        const widthChange = anchor.width - originalAnchor.width
        const changeText = widthChange > 0 ? `+${widthChange}px` : `${widthChange}px`
        ctx.font = '12px Arial'
        ctx.fillStyle = widthChange > 0 ? '#22c55e' : widthChange < 0 ? '#ef4444' : '#6b7280'
        ctx.fillText(`宽度: ${changeText}`, x, y + anchor.height + 15)
      }

      // 如果锚点有动态调整历史，显示调整指示器
      if (anchor.lastDetectedBounds) {
        ctx.fillStyle = '#00ff00'
        ctx.font = '12px Arial'
        ctx.fillText('🔄 动态', x + ctx.measureText(labelText).width + 10, y - 5)

        // 绘制原始边界（虚线）
        if (anchor.originalWidth && anchor.originalHeight) {
          const originalX = anchor.centerX - anchor.originalWidth / 2
          const originalY = anchor.centerY - anchor.originalHeight / 2

          ctx.strokeStyle = anchor.color + '40' // 半透明
          ctx.lineWidth = 1
          ctx.setLineDash([3, 3])
          ctx.strokeRect(originalX, originalY, anchor.originalWidth, anchor.originalHeight)
        }
      }
    })

    // 移除了实时OCR结果显示，因为我们不再进行实时OCR

    // 绘制当前正在绘制的矩形
    if (isDrawing && currentDrawRect) {
      ctx.strokeStyle = '#ff0000'
      ctx.lineWidth = 2
      ctx.setLineDash([5, 5])
      ctx.strokeRect(
        currentDrawRect.x,
        currentDrawRect.y,
        currentDrawRect.width,
        currentDrawRect.height
      )

      // 显示尺寸信息
      ctx.fillStyle = '#ff0000'
      ctx.font = '12px Arial'
      ctx.fillText(
        `${Math.round(currentDrawRect.width)} × ${Math.round(currentDrawRect.height)}`,
        currentDrawRect.x,
        currentDrawRect.y - 5
      )
    }
  }, [anchors, dynamicAnchors, isDrawing, currentDrawRect])

  // 实时模式不再进行OCR处理，只是显示锚点和视频
  // 移除了实时OCR处理，因为我们只在拼图提取时才调用OCR

  // 绘制覆盖层
  useEffect(() => {
    drawOverlay()
  }, [drawOverlay])

  // 手动绘制锚点的鼠标事件
  const handleCanvasMouseDown = useCallback((e: React.MouseEvent<HTMLCanvasElement>) => {
    if (!isManualMode || !canvasRef.current || !videoRef.current) return

    const canvas = canvasRef.current
    const rect = canvas.getBoundingClientRect()
    const video = videoRef.current

    // 计算相对于视频的坐标
    const scaleX = video.videoWidth / rect.width
    const scaleY = video.videoHeight / rect.height

    const x = (e.clientX - rect.left) * scaleX
    const y = (e.clientY - rect.top) * scaleY

    setIsDrawing(true)
    setDrawStart({ x, y })
    setCurrentDrawRect({ x, y, width: 0, height: 0 })
  }, [isManualMode])

  const handleCanvasMouseMove = useCallback((e: React.MouseEvent<HTMLCanvasElement>) => {
    if (!isDrawing || !drawStart || !canvasRef.current || !videoRef.current) return

    const canvas = canvasRef.current
    const rect = canvas.getBoundingClientRect()
    const video = videoRef.current

    const scaleX = video.videoWidth / rect.width
    const scaleY = video.videoHeight / rect.height

    const currentX = (e.clientX - rect.left) * scaleX
    const currentY = (e.clientY - rect.top) * scaleY

    const width = currentX - drawStart.x
    const height = currentY - drawStart.y

    setCurrentDrawRect({
      x: Math.min(drawStart.x, currentX),
      y: Math.min(drawStart.y, currentY),
      width: Math.abs(width),
      height: Math.abs(height)
    })
  }, [isDrawing, drawStart])

  const handleCanvasMouseUp = useCallback(() => {
    if (!isDrawing || !currentDrawRect || !videoRef.current) return

    // 只有当矩形足够大时才创建锚点
    if (currentDrawRect.width > 50 && currentDrawRect.height > 20) {
      const newAnchor: SubtitleAnchor = {
        id: `manual_${Date.now()}`,
        centerX: Math.round(currentDrawRect.x + currentDrawRect.width / 2),
        centerY: Math.round(currentDrawRect.y + currentDrawRect.height / 2),
        width: Math.round(currentDrawRect.width),
        height: Math.round(currentDrawRect.height),
        language: 'zh',
        isPrimary: anchors.length === 0, // 第一个锚点设为主锚点
        color: ANCHOR_COLORS[anchors.length % ANCHOR_COLORS.length]
      }

      setAnchors(prev => [...prev, newAnchor])
      toast.success('手动锚点添加成功')
    }

    // 重置绘制状态
    setIsDrawing(false)
    setDrawStart(null)
    setCurrentDrawRect(null)
  }, [isDrawing, currentDrawRect, anchors.length])

  // 取消绘制
  const handleCanvasMouseLeave = useCallback(() => {
    setIsDrawing(false)
    setDrawStart(null)
    setCurrentDrawRect(null)
  }, [])

  // 优化的锚点检测算法
  const optimizeAnchorDetection = useCallback((ocrResults: OCRResult[], imageData: ImageData) => {
    const anchors: Array<{
      centerX: number
      centerY: number
      width: number
      height: number
      confidence: number
    }> = []

    if (ocrResults.length === 0) {
      // 如果没有检测到文本，创建默认的底部字幕区域
      const defaultAnchor = {
        centerX: imageData.width * 0.5,
        centerY: imageData.height * 0.9, // 底部10%区域
        width: imageData.width * 0.8,
        height: imageData.height * 0.08,
        confidence: 0.8
      }
      anchors.push(defaultAnchor)
      return anchors
    }

    // 按Y坐标分组文本（识别不同行的字幕）
    const textGroups: Array<{
      texts: OCRResult[]
      avgY: number
      minX: number
      maxX: number
      minY: number
      maxY: number
    }> = []

    ocrResults.forEach(result => {
      if (result.confidence < 0.5) return

      const bbox = result.bbox
      const minX = Math.min(...bbox.map(p => p[0]))
      const maxX = Math.max(...bbox.map(p => p[0]))
      const minY = Math.min(...bbox.map(p => p[1]))
      const maxY = Math.max(...bbox.map(p => p[1]))
      const avgY = (minY + maxY) / 2

      // 查找相近Y坐标的组
      let foundGroup = false
      for (const group of textGroups) {
        if (Math.abs(group.avgY - avgY) < 30) { // 30像素容差
          group.texts.push(result)
          group.minX = Math.min(group.minX, minX)
          group.maxX = Math.max(group.maxX, maxX)
          group.minY = Math.min(group.minY, minY)
          group.maxY = Math.max(group.maxY, maxY)
          group.avgY = (group.avgY + avgY) / 2
          foundGroup = true
          break
        }
      }

      if (!foundGroup) {
        textGroups.push({
          texts: [result],
          avgY,
          minX,
          maxX,
          minY,
          maxY
        })
      }
    })

    // 优先选择底部的文本组作为字幕锚点
    textGroups.sort((a, b) => b.avgY - a.avgY) // 按Y坐标降序排列

    // 为每个文本组创建锚点
    textGroups.forEach((group, index) => {
      // 扩展锚点区域以包含可能的字幕变化
      const padding = 20
      const width = Math.max(group.maxX - group.minX + padding * 2, imageData.width * 0.3)
      const height = Math.max(group.maxY - group.minY + padding, 40)

      const centerX = (group.minX + group.maxX) / 2
      const centerY = (group.minY + group.maxY) / 2

      // 确保锚点在视频范围内
      const finalCenterX = Math.max(width / 2, Math.min(imageData.width - width / 2, centerX))
      const finalCenterY = Math.max(height / 2, Math.min(imageData.height - height / 2, centerY))

      const avgConfidence = group.texts.reduce((sum, text) => sum + text.confidence, 0) / group.texts.length

      anchors.push({
        centerX: finalCenterX,
        centerY: finalCenterY,
        width,
        height,
        confidence: avgConfidence
      })
    })

    // 如果没有检测到合适的锚点，添加默认底部区域
    if (anchors.length === 0) {
      anchors.push({
        centerX: imageData.width * 0.5,
        centerY: imageData.height * 0.9,
        width: imageData.width * 0.8,
        height: imageData.height * 0.08,
        confidence: 0.6
      })
    }

    return anchors
  }, [])

  // 删除锚点
  const removeAnchor = (anchorId: string) => {
    setAnchors(prev => {
      const newAnchors = prev.filter(anchor => anchor.id !== anchorId)
      // 如果删除的是主锚点，将第一个锚点设为主锚点
      if (newAnchors.length > 0 && !newAnchors.some(a => a.isPrimary)) {
        newAnchors[0].isPrimary = true
      }
      return newAnchors
    })
    toast.success('锚点已删除')
  }

  // 清除所有锚点
  const clearAllAnchors = () => {
    setAnchors([])
    toast.success('所有锚点已清除')
  }

  // 初始化智能帧提取器
  useEffect(() => {
    if (videoRef.current && !frameExtractor) {
      const extractor = new SmartFrameExtractor(videoRef.current, {
        fps: fps,
        skipSimilarFrames: smartExtractionConfig.skipSimilarFrames,
        similarityThreshold: smartExtractionConfig.similarityThreshold,
        batchSize: smartExtractionConfig.batchSize,
        maxConcurrentBatches: smartExtractionConfig.maxConcurrentBatches,
        adaptiveSampling: smartExtractionConfig.adaptiveSampling
      })
      setFrameExtractor(extractor)
    }
  }, [videoRef.current, smartExtractionConfig, fps])



  // 智能批量提取字幕
  const handleSmartExtractSubtitles = useCallback(async () => {
    console.log('🚀 开始智能字幕提取检查...')

    // 详细的前置条件检查
    if (!videoFile) {
      toast.error('请先选择视频文件')
      console.error('❌ 缺少视频文件')
      return
    }

    if (!ocrProcessor) {
      toast.error('OCR处理器未加载，请等待模型初始化完成')
      console.error('❌ OCR处理器未初始化')
      return
    }

    if (anchors.length === 0) {
      toast.error('请先标记字幕锚点')
      console.error('❌ 没有锚点')
      return
    }

    if (!frameExtractor) {
      toast.error('帧提取器未初始化')
      console.error('❌ 帧提取器未初始化')
      return
    }

    const primaryAnchors = anchors.filter(anchor => anchor.isPrimary)
    if (primaryAnchors.length === 0) {
      toast.error('请至少设置一个主字幕锚点')
      console.error('❌ 没有主锚点')
      return
    }

    console.log(`✅ 前置检查通过: 视频=${videoFile.name}, OCR=${processorType}, 锚点=${anchors.length}, 主锚点=${primaryAnchors.length}`)

    // 重置停止标志
    setShouldStop(false)

    setSubtitles([])
    setProgress({
      isRunning: true,
      currentFrame: 0,
      totalFrames: Math.ceil(duration), // 按秒计算
      currentTime: 0,
      totalTime: duration,
      speed: 0,
      stage: 'extracting'
    })

    const extractedSubtitles: SubtitleResult[] = []
    const startTime = Date.now()

    try {
      // 智能提取关键帧（按秒采样）
      toast.info('开始智能帧提取（逐秒采样）...')

      // 临时修改frameExtractor的配置为按秒采样
      const originalConfig = frameExtractor.getConfig()
      frameExtractor.updateConfig({ fps: 1 }) // 设置为1fps，即每秒1帧

      const frames = await frameExtractor.extractFramesBatch(0, duration)

      // 恢复原始配置
      frameExtractor.updateConfig(originalConfig)

      // 过滤出有效帧（跳过相似帧）
      const validFrames = frames.filter(frame => frame.imageData && frame.imageData.length > 0)
      const skippedFrames = frames.length - validFrames.length

      toast.info(`提取完成：${validFrames.length} 有效帧，跳过 ${skippedFrames} 相似帧`)

      if (validFrames.length === 0) {
        toast.warning('没有提取到有效帧')
        setProgress(prev => ({ ...prev, isRunning: false, stage: 'error' }))
        return
      }

      // 批量OCR处理
      toast.info('开始批量OCR识别...')
      console.log(`🎯 准备批量OCR: ${validFrames.length} 帧, ${primaryAnchors.length} 主锚点`)

      let batchResults
      try {
        batchResults = await processBatchOCR(validFrames, primaryAnchors)

        if (!batchResults || !batchResults.success) {
          const errorMsg = batchResults?.error || '批量OCR处理失败，原因未知'
          console.error(`❌ 批量OCR失败:`, errorMsg)
          throw new Error(errorMsg)
        }

        console.log(`✅ 批量OCR成功:`, {
          totalResults: batchResults.results?.length,
          processedFrames: batchResults.processedFrames,
          skippedFrames: batchResults.skippedFrames
        })

        if (!batchResults.results || batchResults.results.length === 0) {
          console.warn(`⚠️ 批量OCR没有返回任何结果`)
          toast.warning('OCR处理完成但没有识别到文字，请检查锚点位置和视频质量')
        }

      } catch (ocrError) {
        console.error(`❌ 批量OCR异常:`, ocrError)
        toast.error(`OCR处理失败: ${ocrError}`)
        throw ocrError
      }

      // 处理OCR结果，生成字幕
      let lastSubtitleText = ''
      let lastSubtitleStart = 0

      batchResults.results?.forEach((result, index) => {
        // 检查是否需要停止
        if (shouldStop) {
          console.log(`🛑 在智能提取第 ${index} 帧处停止`)
          return
        }

        const currentText = result.texts.join(' ').trim()

        if (currentText && currentText !== lastSubtitleText) {
          // 保存上一个字幕
          if (lastSubtitleText && result.timestamp - lastSubtitleStart >= minSubtitleDuration / 1000) {
            extractedSubtitles.push({
              id: `subtitle_${extractedSubtitles.length}`,
              startTime: lastSubtitleStart,
              endTime: result.timestamp,
              text: lastSubtitleText,
              confidence: result.confidence,
              anchorId: primaryAnchors[0].id
            })
          }

          // 开始新字幕
          lastSubtitleText = currentText
          lastSubtitleStart = result.timestamp
        }

        // 更新进度
        setProgress(prev => ({
          ...prev,
          currentFrame: index,
          currentTime: result.timestamp,
          speed: index / ((Date.now() - startTime) / 1000)
        }))
      })

      // 处理最后一个字幕
      if (lastSubtitleText && duration - lastSubtitleStart >= minSubtitleDuration / 1000) {
        extractedSubtitles.push({
          id: `subtitle_${extractedSubtitles.length}`,
          startTime: lastSubtitleStart,
          endTime: duration,
          text: lastSubtitleText,
          confidence: 0.9,
          anchorId: primaryAnchors[0].id
        })
      }

      // 更新性能统计
      const totalTime = Date.now() - startTime
      const stats = frameExtractor.getStats()

      setPerformanceStats({
        avgProcessingTime: totalTime / validFrames.length,
        totalProcessedFrames: validFrames.length,
        speedupRatio: (duration * 1000) / totalTime,
        skippedFrames: skippedFrames,
        cacheHitRate: stats.cacheHitRate
      })

      setSubtitles(extractedSubtitles)
      setProgress(prev => ({ ...prev, isRunning: false, stage: 'completed' }))

      if (extractedSubtitles.length > 0) {
        toast.success(`智能提取完成！共提取到 ${extractedSubtitles.length} 条字幕，跳过 ${skippedFrames} 相似帧`)
      } else {
        toast.warning('未检测到字幕内容，请检查锚点设置和视频质量')
      }
    } catch (error) {
      console.error('智能字幕提取失败:', error)
      setProgress(prev => ({ ...prev, isRunning: false, stage: 'error' }))
      toast.error('智能字幕提取失败，请重试')
    } finally {
      // 重置停止标志
      setShouldStop(false)
    }
  }, [videoFile, ocrProcessor, anchors, duration, fps, minSubtitleDuration, frameExtractor, shouldStop])

  // 新的拼图式字幕提取
  const handleMosaicExtractSubtitles = useCallback(async () => {
    if (!videoFile || !ocrProcessor || anchors.length === 0) {
      toast.error('请先选择视频文件、等待模型加载并分析锚点')
      return
    }

    const primaryAnchors = anchors.filter(a => a.isPrimary)
    if (primaryAnchors.length === 0) {
      toast.error('请至少设置一个主字幕锚点')
      return
    }

    // 优先使用百度OCR，但也支持其他OCR服务
    if (!(ocrProcessor instanceof BaiduOCRProcessor)) {
      console.log('使用非百度OCR服务进行拼图提取')
      toast.info('使用当前OCR服务进行拼图提取')
    }

    setShouldStop(false)
    setProgress(prev => ({
      ...prev,
      isRunning: true,
      stage: 'extracting',
      currentFrame: 0,
      totalFrames: Math.floor(duration),
      totalTime: duration
    }))

    try {
      console.log('🚀 开始拼图式字幕提取')

      // 第1步：批量截取标记的字幕锚点区域图片（不调用OCR）
      setProgress(prev => ({ ...prev, stage: 'extracting' }))
      toast.info('第1步：批量截取字幕锚点区域...')

      const anchorFrames = await mosaicProcessor.extractAnchorFrames(
        videoRef.current!,
        primaryAnchors,
        duration,
        (progress) => {
          setProgress(prev => ({
            ...prev,
            currentFrame: Math.floor(progress * duration),
            currentTime: progress * duration
          }))
        }
      )

      console.log(`✅ 截取完成: 获得 ${anchorFrames.length} 个锚点帧`)

      // 第2步：将截取的字幕锚点区域图片进行拼图合并（不调用OCR）
      toast.info('第2步：创建拼图合并图像...')
      const mosaicImages = await mosaicProcessor.createMosaicImages(anchorFrames)

      console.log(`✅ 拼图创建完成: 生成 ${mosaicImages.length} 张拼图`)

      // 第3步：OCR识别拼图（这里才开始调用OCR API）
      toast.info(`第3步：OCR识别 ${mosaicImages.length} 张拼图...`)
      const allMappedSubtitles: MappedSubtitle[] = []

      for (let i = 0; i < mosaicImages.length; i++) {
        const mosaic = mosaicImages[i]

        try {
          console.log(`🔍 识别第 ${i + 1}/${mosaicImages.length} 张拼图...`)

          // 这里才是真正的OCR API调用
          const ocrResults = await ocrProcessor.processImage(mosaic.imageData)

          // 第4步：结果映射
          const mappedSubtitles = mosaicProcessor.mapOCRResultsToTimestamps(ocrResults, mosaic)
          allMappedSubtitles.push(...mappedSubtitles)

          console.log(`✅ 第 ${i + 1} 张拼图识别完成，获得 ${mappedSubtitles.length} 个字幕`)

        } catch (error) {
          console.warn(`第 ${i + 1} 张拼图识别失败:`, error)

          // 检查是否是API限制错误
          if (error.message && error.message.includes('daily request limit reached')) {
            setApiLimitReached(true)
            toast.error('百度OCR API每日请求限制已达到，提取中止')
            throw new Error('API限制已达到')
          }
        }
      }

      // 转换为标准字幕格式
      const extractedSubtitles: SubtitleResult[] = allMappedSubtitles.map((subtitle, index) => ({
        id: `subtitle-${index}`,
        startTime: subtitle.timestamp,
        endTime: subtitle.timestamp + 1, // 默认1秒时长
        text: subtitle.text,
        confidence: subtitle.confidence,
        anchorId: subtitle.anchorId
      }))

      setSubtitles(extractedSubtitles)
      setProgress(prev => ({ ...prev, isRunning: false, stage: 'completed' }))

      if (extractedSubtitles.length > 0) {
        toast.success(
          `拼图提取完成！共提取到 ${extractedSubtitles.length} 条字幕，` +
          `调用OCR ${mosaicImages.length} 次（而不是 ${anchorFrames.length} 次）`
        )
      } else {
        toast.warning('未检测到字幕内容，请检查锚点设置和视频质量')
      }
    } catch (error) {
      console.error('拼图字幕提取失败:', error)
      setProgress(prev => ({ ...prev, isRunning: false, stage: 'error' }))
      toast.error('拼图字幕提取失败，请重试')
    } finally {
      setShouldStop(false)
    }
  }, [videoFile, ocrProcessor, anchors, duration, mosaicProcessor, shouldStop])

  // 批量OCR处理（增强版，带详细错误处理和降级机制）
  const processBatchOCR = async (frames: FrameData[], anchors: SubtitleAnchor[]) => {
    console.log(`🚀 开始批量OCR处理: ${frames.length} 帧, ${anchors.length} 锚点`)

    try {
      // 验证输入数据
      if (!frames || frames.length === 0) {
        throw new Error('没有有效的帧数据')
      }

      if (!anchors || anchors.length === 0) {
        throw new Error('没有有效的锚点数据')
      }

      // 检查帧数据完整性
      const validFrames = frames.filter(frame =>
        frame &&
        frame.imageData &&
        frame.imageData.length > 0 &&
        typeof frame.timestamp === 'number'
      )

      if (validFrames.length === 0) {
        throw new Error('所有帧数据都无效')
      }

      if (validFrames.length < frames.length) {
        console.warn(`⚠️ 过滤掉 ${frames.length - validFrames.length} 个无效帧`)
      }

      const batchData = {
        frames: validFrames.map(frame => ({
          timestamp: frame.timestamp,
          imageData: frame.imageData
        })),
        anchors: anchors.map(anchor => ({
          centerX: anchor.centerX,
          centerY: anchor.centerY,
          width: anchor.width,
          height: anchor.height
        })),
        skipSimilar: smartExtractionConfig.skipSimilarFrames,
        similarityThreshold: smartExtractionConfig.similarityThreshold
      }

      console.log(`📤 发送批量OCR请求: ${validFrames.length} 帧`)

      const formData = new FormData()
      formData.append('batchData', JSON.stringify(batchData))

      const response = await fetch('/api/ocr/batch-extract', {
        method: 'POST',
        body: formData,
        headers: {
          // 不设置Content-Type，让浏览器自动设置multipart/form-data
        }
      })

      console.log(`📥 批量OCR响应状态: ${response.status}`)

      if (!response.ok) {
        const errorText = await response.text()
        console.error(`❌ 批量OCR请求失败: ${response.status}`, errorText)

        // 尝试降级到逐帧处理
        console.log(`🔄 降级到逐帧OCR处理...`)
        return await fallbackToFrameByFrame(validFrames, anchors)
      }

      const result = await response.json()
      console.log(`✅ 批量OCR处理完成:`, {
        success: result.success,
        processedFrames: result.processedFrames,
        skippedFrames: result.skippedFrames,
        totalResults: result.results?.length
      })

      return result

    } catch (error) {
      console.error(`❌ 批量OCR处理异常:`, error)

      // 降级到逐帧处理
      console.log(`🔄 异常降级到逐帧OCR处理...`)
      try {
        return await fallbackToFrameByFrame(frames, anchors)
      } catch (fallbackError) {
        console.error(`❌ 降级处理也失败:`, fallbackError)
        throw new Error(`批量OCR和降级处理都失败: ${error}`)
      }
    }
  }

  // 降级到逐帧OCR处理
  const fallbackToFrameByFrame = async (frames: FrameData[], anchors: SubtitleAnchor[]) => {
    console.log(`🔄 开始逐帧OCR降级处理: ${frames.length} 帧`)

    if (!ocrProcessor) {
      throw new Error('OCR处理器未初始化，无法进行降级处理')
    }

    const results = []
    let processedFrames = 0
    let skippedFrames = 0

    for (let i = 0; i < frames.length; i++) {
      const frame = frames[i]

      try {
        // 检查是否需要停止
        if (shouldStop) {
          console.log(`🛑 降级处理在第 ${i} 帧停止`)
          break
        }

        // 解码图像数据
        const imageData = await decodeFrameImageData(frame.imageData)
        if (!imageData) {
          console.warn(`⚠️ 帧 ${i} 图像数据解码失败`)
          skippedFrames++
          continue
        }

        // 对每个锚点进行OCR识别
        const frameTexts: string[] = []
        let totalConfidence = 0
        let anchorCount = 0

        for (const anchor of anchors) {
          try {
            const { results: anchorResults } = await processAnchorOCR(imageData, anchor)

            const anchorTexts = anchorResults
              .filter(result => result.confidence > 0.5)
              .map(result => result.text.trim())
              .filter(text => text.length > 0)

            frameTexts.push(...anchorTexts)

            // 计算平均置信度
            anchorResults.forEach(result => {
              if (result.confidence > 0.5) {
                totalConfidence += result.confidence
                anchorCount++
              }
            })
          } catch (anchorError) {
            console.warn(`⚠️ 锚点 ${anchor.id} 处理失败:`, anchorError)
          }
        }

        const avgConfidence = anchorCount > 0 ? totalConfidence / anchorCount : 0

        results.push({
          timestamp: frame.timestamp,
          texts: frameTexts,
          confidence: avgConfidence,
          skipped: false
        })

        processedFrames++

        // 更新进度
        if (i % 5 === 0) {
          setProgress(prev => ({
            ...prev,
            currentFrame: i,
            totalFrames: frames.length
          }))
        }

      } catch (frameError) {
        console.warn(`⚠️ 帧 ${i} 处理失败:`, frameError)
        skippedFrames++
      }
    }

    console.log(`✅ 降级处理完成: 处理 ${processedFrames} 帧, 跳过 ${skippedFrames} 帧`)

    return {
      success: true,
      results,
      processedFrames,
      skippedFrames
    }
  }

  // 解码帧图像数据
  const decodeFrameImageData = async (imageDataStr: string): Promise<ImageData | null> => {
    try {
      return new Promise((resolve, reject) => {
        const img = new Image()
        img.onload = () => {
          const canvas = document.createElement('canvas')
          const ctx = canvas.getContext('2d')!
          canvas.width = img.width
          canvas.height = img.height
          ctx.drawImage(img, 0, 0)
          const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height)
          resolve(imageData)
        }
        img.onerror = () => reject(new Error('图像加载失败'))
        img.src = imageDataStr
      })
    } catch (error) {
      console.error('图像数据解码失败:', error)
      return null
    }
  }

  // 字幕文本相似度比较
  const isSubtitleSimilar = (text1: string, text2: string, threshold: number = 0.8): boolean => {
    if (!text1 || !text2) return false
    if (text1 === text2) return true

    // 简单的相似度计算（基于编辑距离）
    const maxLen = Math.max(text1.length, text2.length)
    if (maxLen === 0) return true

    const distance = levenshteinDistance(text1, text2)
    const similarity = 1 - distance / maxLen

    return similarity >= threshold
  }

  // 计算编辑距离
  const levenshteinDistance = (str1: string, str2: string): number => {
    const matrix = []

    for (let i = 0; i <= str2.length; i++) {
      matrix[i] = [i]
    }

    for (let j = 0; j <= str1.length; j++) {
      matrix[0][j] = j
    }

    for (let i = 1; i <= str2.length; i++) {
      for (let j = 1; j <= str1.length; j++) {
        if (str2.charAt(i - 1) === str1.charAt(j - 1)) {
          matrix[i][j] = matrix[i - 1][j - 1]
        } else {
          matrix[i][j] = Math.min(
            matrix[i - 1][j - 1] + 1,
            matrix[i][j - 1] + 1,
            matrix[i - 1][j] + 1
          )
        }
      }
    }

    return matrix[str2.length][str1.length]
  }

  // 动态调整锚点大小（减少反复横跳）
  const adjustAnchorDynamically = (anchor: SubtitleAnchor, ocrResults: OCRResult[]): SubtitleAnchor => {
    if (ocrResults.length === 0) {
      return anchor
    }

    // 计算所有检测到的文本的边界
    let minX = Infinity, maxX = -Infinity, minY = Infinity, maxY = -Infinity

    ocrResults.forEach(result => {
      if (result.bbox && result.bbox.length >= 4) {
        const bbox = result.bbox
        const textMinX = Math.min(...bbox.map(p => p[0]))
        const textMaxX = Math.max(...bbox.map(p => p[0]))
        const textMinY = Math.min(...bbox.map(p => p[1]))
        const textMaxY = Math.max(...bbox.map(p => p[1]))

        minX = Math.min(minX, textMinX)
        maxX = Math.max(maxX, textMaxX)
        minY = Math.min(minY, textMinY)
        maxY = Math.max(maxY, textMaxY)
      }
    })

    if (minX === Infinity) {
      return anchor
    }

    // 计算检测到的尺寸和位置
    const detectedWidth = maxX - minX
    const detectedHeight = maxY - minY
    const detectedCenterX = (minX + maxX) / 2
    const detectedCenterY = (minY + maxY) / 2

    // 设置调整阈值，针对实时提取优化
    const POSITION_THRESHOLD_X = 15 // X位置变化阈值（像素）- 对水平位置更宽容
    const POSITION_THRESHOLD_Y = 8  // Y位置变化阈值（像素）- 对垂直位置更严格
    const WIDTH_THRESHOLD = 20      // 宽度变化阈值（像素）- 对宽度变化更敏感
    const HEIGHT_THRESHOLD = 10     // 高度变化阈值（像素）

    // 检查是否需要调整
    const positionChangeX = Math.abs(detectedCenterX - anchor.centerX)
    const positionChangeY = Math.abs(detectedCenterY - anchor.centerY)
    const sizeChangeW = Math.abs(detectedWidth - anchor.width)
    const sizeChangeH = Math.abs(detectedHeight - anchor.height)

    // 智能调整策略：优先调整宽度以适应文本长度变化
    const shouldAdjustWidth = sizeChangeW > WIDTH_THRESHOLD
    const shouldAdjustHeight = sizeChangeH > HEIGHT_THRESHOLD
    const shouldAdjustPosition = positionChangeX > POSITION_THRESHOLD_X || positionChangeY > POSITION_THRESHOLD_Y

    // 如果没有显著变化，不进行调整
    if (!shouldAdjustWidth && !shouldAdjustHeight && !shouldAdjustPosition) {
      return anchor
    }

    // 初始化动态属性
    if (!anchor.originalWidth) {
      anchor.originalWidth = anchor.width
      anchor.originalHeight = anchor.height
      anchor.minWidth = anchor.width * 0.5
      anchor.maxWidth = anchor.width * 2
      anchor.expandRatio = 1.2
    }

    // 智能调整策略
    let newWidth = anchor.width
    let newHeight = anchor.height
    let newCenterX = anchor.centerX
    let newCenterY = anchor.centerY

    // 宽度调整：优先适应文本长度变化
    if (shouldAdjustWidth) {
      const targetWidth = Math.max(detectedWidth * 1.1, anchor.originalWidth! * 0.8) // 给文本留10%边距
      newWidth = Math.max(
        anchor.minWidth!,
        Math.min(
          anchor.maxWidth!,
          anchor.width * 0.7 + targetWidth * 0.3 // 平滑过渡
        )
      )
    }

    // 高度调整：保持相对稳定
    if (shouldAdjustHeight) {
      const targetHeight = Math.max(detectedHeight * 1.2, anchor.originalHeight! * 0.9)
      newHeight = Math.max(
        anchor.originalHeight! * 0.8,
        Math.min(
          anchor.originalHeight! * 1.3,
          anchor.height * 0.8 + targetHeight * 0.2 // 更保守的高度调整
        )
      )
    }

    // 位置调整：X坐标适应文本中心，Y坐标保持稳定
    if (shouldAdjustPosition) {
      newCenterX = anchor.centerX * 0.85 + detectedCenterX * 0.15 // 轻微跟随文本中心
      newCenterY = anchor.centerY * 0.95 + detectedCenterY * 0.05 // 保持垂直位置稳定
    }

    // 保存检测边界用于下次参考
    const updatedAnchor: SubtitleAnchor = {
      ...anchor,
      centerX: newCenterX,
      centerY: newCenterY,
      width: newWidth,
      height: newHeight,
      lastDetectedBounds: { minX, maxX, minY, maxY }
    }

    return updatedAnchor
  }

  // 处理单个锚点的OCR识别
  const processAnchorOCR = async (imageData: ImageData, anchor: SubtitleAnchor): Promise<{
    results: OCRResult[]
    adjustedAnchor: SubtitleAnchor
  }> => {
    if (!ocrProcessor) {
      throw new Error('OCR处理器未初始化')
    }

    console.log(`🎯 处理锚点 ${anchor.id}:`, {
      centerX: anchor.centerX,
      centerY: anchor.centerY,
      width: anchor.width,
      height: anchor.height,
      imageSize: `${imageData.width}x${imageData.height}`
    })

    // 裁剪锚点区域
    const { croppedImage, offsetX, offsetY } = cropAnchorRegion(imageData, anchor)

    console.log(`📐 裁剪区域:`, {
      offsetX,
      offsetY,
      croppedSize: `${croppedImage.width}x${croppedImage.height}`
    })

    // 只对裁剪区域进行OCR识别
    const ocrResults = await ocrProcessor.processImage(croppedImage)

    console.log(`🔍 OCR识别结果:`, {
      anchorId: anchor.id,
      resultCount: ocrResults.length,
      results: ocrResults.map(r => ({
        text: r.text,
        confidence: r.confidence,
        bbox: r.bbox
      }))
    })

    // 详细的置信度分析
    if (ocrResults.length > 0) {
      const confidences = ocrResults.map(r => r.confidence)
      const avgConfidence = confidences.reduce((a, b) => a + b, 0) / confidences.length
      const maxConfidence = Math.max(...confidences)
      const minConfidence = Math.min(...confidences)

      console.log(`📊 置信度分析:`, {
        '平均': avgConfidence.toFixed(3),
        '最高': maxConfidence.toFixed(3),
        '最低': minConfidence.toFixed(3),
        '高于0.5的数量': ocrResults.filter(r => r.confidence > 0.5).length,
        '高于0.7的数量': ocrResults.filter(r => r.confidence > 0.7).length
      })
    } else {
      console.warn(`⚠️ 锚点 ${anchor.id} 未检测到任何文本`)
    }

    // 将相对坐标转换为绝对坐标
    const absoluteResults = ocrResults.map(result => ({
      ...result,
      bbox: result.bbox.map(point => [
        point[0] + offsetX,
        point[1] + offsetY
      ])
    }))

    // 动态调整锚点
    const adjustedAnchor = adjustAnchorDynamically(anchor, absoluteResults)

    return {
      results: absoluteResults,
      adjustedAnchor
    }
  }

  // 测试单个锚点的OCR识别
  const testSingleAnchor = async (anchor: SubtitleAnchor) => {
    if (!videoRef.current || !ocrProcessor) {
      toast.error('请先加载视频和OCR处理器')
      return
    }

    setTestingAnchor(true)

    try {
      // 获取当前帧
      const currentTime = videoRef.current.currentTime
      const imageData = await extractFrameImageData(currentTime)

      console.log(`🧪 测试锚点 ${anchor.id} 在时间 ${currentTime.toFixed(2)}s`)

      // 处理锚点OCR
      const { results, adjustedAnchor } = await processAnchorOCR(imageData, anchor)

      // 更新锚点状态（动态调整）
      setAnchors(prevAnchors =>
        prevAnchors.map(a =>
          a.id === anchor.id ? adjustedAnchor : a
        )
      )

      console.log(`🔄 测试中动态调整锚点 ${anchor.id}:`, {
        原始: { x: anchor.centerX, y: anchor.centerY, w: anchor.width, h: anchor.height },
        调整后: { x: adjustedAnchor.centerX, y: adjustedAnchor.centerY, w: adjustedAnchor.width, h: adjustedAnchor.height }
      })

      // 生成裁剪图像的预览
      const { croppedImage } = cropAnchorRegion(imageData, anchor)
      const canvas = document.createElement('canvas')
      const ctx = canvas.getContext('2d')!
      canvas.width = croppedImage.width
      canvas.height = croppedImage.height
      ctx.putImageData(croppedImage, 0, 0)
      const croppedImageUrl = canvas.toDataURL()

      // 保存调试信息
      setDebugImages(prev => [{
        anchorId: anchor.id,
        croppedImage: croppedImageUrl,
        ocrResults: results
      }, ...prev.slice(0, 4)]) // 只保留最近5个

      if (results.length > 0) {
        const texts = results.map(r => r.text).join(' ')
        toast.success(`锚点 ${anchor.id} 识别成功: "${texts}"`)
      } else {
        toast.warning(`锚点 ${anchor.id} 未识别到文本`)
      }

    } catch (error) {
      console.error('测试锚点失败:', error)
      toast.error(`测试锚点失败: ${error}`)
    } finally {
      setTestingAnchor(false)
    }
  }

  // 测试当前时间点的字幕提取
  const testCurrentFrame = async () => {
    if (!videoRef.current || !ocrProcessor || anchors.length === 0) {
      toast.error('请先加载视频、OCR处理器并标记锚点')
      return
    }

    setTestingAnchor(true)

    try {
      const currentTime = videoRef.current.currentTime
      const imageData = await extractFrameImageData(currentTime)
      const primaryAnchors = anchors.filter(anchor => anchor.isPrimary)

      console.log(`🧪 测试当前帧 ${currentTime.toFixed(2)}s，锚点数量: ${primaryAnchors.length}`)

      let allTexts: string[] = []

      for (const anchor of primaryAnchors) {
        try {
          const { results, adjustedAnchor } = await processAnchorOCR(imageData, anchor)

          // 更新锚点状态（动态调整）
          setAnchors(prevAnchors =>
            prevAnchors.map(a =>
              a.id === anchor.id ? adjustedAnchor : a
            )
          )

          console.log(`🔄 测试当前帧动态调整锚点 ${anchor.id}:`, {
            原始: { x: anchor.centerX, y: anchor.centerY, w: anchor.width, h: anchor.height },
            调整后: { x: adjustedAnchor.centerX, y: adjustedAnchor.centerY, w: adjustedAnchor.width, h: adjustedAnchor.height }
          })

          const anchorTexts = results
            .filter(result => result.confidence > 0.5)
            .map(result => result.text.trim())
            .filter(text => text.length > 0)

          allTexts.push(...anchorTexts)
        } catch (error) {
          console.warn(`处理锚点 ${anchor.id} 失败:`, error)
        }
      }

      const currentText = allTexts.join(' ').trim()

      if (currentText) {
        toast.success(`当前帧识别成功: "${currentText}"`)
        console.log(`✅ 当前帧字幕: "${currentText}"`)
      } else {
        toast.warning('当前帧未识别到字幕文本')
        console.log(`❌ 当前帧无字幕`)
      }

    } catch (error) {
      console.error('测试当前帧失败:', error)
      toast.error(`测试当前帧失败: ${error}`)
    } finally {
      setTestingAnchor(false)
    }
  }

  return (
    <div className="h-full flex flex-col bg-gray-50 dark:bg-gray-900">
      {/* 头部标题 */}
      <div className="flex-shrink-0 p-6 border-b bg-white dark:bg-gray-800">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold flex items-center">
              <FileText className="h-6 w-6 mr-2" />
              硬字幕提取
            </h1>
            <p className="text-sm text-muted-foreground mt-1">
              使用PP-OCRv5模型从视频中自动识别和提取硬字幕
            </p>
          </div>
          <div className="flex items-center space-x-2">
            <Badge variant="secondary" className="text-xs">
              {processorType === 'baidu' ? '百度OCR服务' :
               processorType === 'backend' ? '后端OCR服务' :
               'OCR处理器 (演示模式)'}
            </Badge>
            {isModelLoading && (
              <Badge variant="outline" className="text-xs">
                <Loader2 className="h-3 w-3 mr-1 animate-spin" />
                连接中...
              </Badge>
            )}
            {ocrProcessor && !isModelLoading && (
              <Badge variant="outline" className={`text-xs ${
                processorType === 'baidu' ? 'text-green-600' :
                processorType === 'backend' ? 'text-purple-600' :
                'text-orange-600'
              }`}>
                <CheckCircle2 className="h-3 w-3 mr-1" />
                {processorType === 'baidu' ? '百度OCR就绪' :
                 processorType === 'backend' ? '后端服务就绪' :
                 '演示处理器就绪'}
              </Badge>
            )}
          </div>
        </div>
      </div>

      {/* 主要内容区域 */}
      <div className="flex-1 flex overflow-hidden">
        {/* 左侧视频预览区 */}
        <div className="flex-1 flex flex-col p-6">
          {/* 视频上传区域 */}
          {!videoFile ? (
            <Card className="flex-1 border-2 border-dashed border-gray-300 dark:border-gray-600">
              <CardContent 
                className="flex-1 flex flex-col items-center justify-center p-8 cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
                onDrop={handleDrop}
                onDragOver={(e) => e.preventDefault()}
                onClick={() => fileInputRef.current?.click()}
              >
                <Upload className="h-12 w-12 text-gray-400 mb-4" />
                <h3 className="text-lg font-medium mb-2">上传视频文件</h3>
                <p className="text-sm text-muted-foreground text-center mb-4">
                  拖拽视频文件到此处，或点击选择文件<br />
                  支持 MP4、AVI、MOV 等格式
                </p>
                <Button variant="outline">
                  <Upload className="h-4 w-4 mr-2" />
                  选择文件
                </Button>
                <input
                  ref={fileInputRef}
                  type="file"
                  accept="video/*"
                  className="hidden"
                  onChange={(e) => {
                    const file = e.target.files?.[0]
                    if (file) handleFileUpload(file)
                  }}
                />
              </CardContent>
            </Card>
          ) : (
            /* 视频播放器 */
            <div className="flex-1 flex flex-col">
              <Card className="flex-1">
                <CardContent className="p-4 h-full flex flex-col">
                  <div className="flex-1 relative bg-black rounded-lg overflow-hidden">
                    <video
                      ref={videoRef}
                      src={videoUrl}
                      className="w-full h-full object-contain"
                      onLoadedMetadata={handleVideoLoad}
                      onTimeUpdate={handleTimeUpdate}
                      onPlay={() => setIsPlaying(true)}
                      onPause={() => setIsPlaying(false)}
                    />
                    
                    {/* 字幕锚点覆盖层 */}
                    {(showAnchors || isRealtimeMode || isManualMode) && (
                      <canvas
                        ref={canvasRef}
                        className={`absolute inset-0 ${isManualMode ? 'cursor-crosshair' : 'pointer-events-none'}`}
                        style={{
                          zIndex: 10,
                          width: '100%',
                          height: '100%',
                          objectFit: 'contain'
                        }}
                        onMouseDown={isManualMode ? handleCanvasMouseDown : undefined}
                        onMouseMove={isManualMode ? handleCanvasMouseMove : undefined}
                        onMouseUp={isManualMode ? handleCanvasMouseUp : undefined}
                        onMouseLeave={isManualMode ? handleCanvasMouseLeave : undefined}
                      />
                    )}
                  </div>
                  
                  {/* 视频控制栏 */}
                  <div className="mt-4 space-y-3">
                    <div className="flex items-center space-x-4">
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={togglePlayPause}
                        disabled={!videoFile}
                      >
                        {isPlaying ? <Pause className="h-4 w-4" /> : <Play className="h-4 w-4" />}
                      </Button>
                      
                      <div className="flex-1">
                        <Slider
                          value={[currentTime]}
                          max={duration}
                          step={0.1}
                          onValueChange={([value]) => seekTo(value)}
                          className="w-full"
                        />
                      </div>
                      
                      <span className="text-sm text-muted-foreground min-w-[100px]">
                        {formatTime(currentTime)} / {formatTime(duration)}
                      </span>
                    </div>

                    {/* 实时模式控制 */}
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <Button
                          size="sm"
                          variant={isRealtimeMode ? "default" : "outline"}
                          onClick={() => {
                            if (isRealtimeMode) {
                              // 停止实时识别
                              setIsRealtimeMode(false)
                            } else {
                              // 开始实时识别并触发字幕提取
                              setIsRealtimeMode(true)
                              // 延迟一下确保状态更新后再开始提取
                              setTimeout(() => {
                                handleMosaicExtractSubtitles()
                              }, 100)
                            }
                          }}
                          disabled={!isRealtimeMode && (!videoFile || anchors.length === 0 || progress.isRunning)}
                        >
                          <Eye className="h-4 w-4 mr-1" />
                          {isRealtimeMode ? '停止实时识别' : '开始实时识别'}
                        </Button>

                        <Button
                          size="sm"
                          variant={showAnchors ? "default" : "outline"}
                          onClick={() => setShowAnchors(!showAnchors)}
                        >
                          <Target className="h-4 w-4 mr-1" />
                          {showAnchors ? '隐藏锚点' : '显示锚点'}
                        </Button>

                        <Button
                          size="sm"
                          variant={isManualMode ? "default" : "outline"}
                          onClick={() => {
                            setIsManualMode(!isManualMode)
                            if (isManualMode) {
                              // 退出手动模式时清理绘制状态
                              setIsDrawing(false)
                              setDrawStart(null)
                              setCurrentDrawRect(null)
                            }
                          }}
                        >
                          <Plus className="h-4 w-4 mr-1" />
                          {isManualMode ? '退出手动标记' : '手动标记锚点'}
                        </Button>
                      </div>



                      {isManualMode && (
                        <Badge variant="outline" className="text-xs text-red-600">
                          手动标记模式：在视频上拖拽绘制锚点
                        </Badge>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          )}
        </div>

        {/* 右侧控制面板 */}
        <div className="w-80 flex-shrink-0 border-l bg-white dark:bg-gray-800 flex flex-col h-full overflow-hidden">
          {/* 模型状态提示 - 固定在顶部 */}
          <div className={`p-4 border-b flex-shrink-0 ${
            processorType === 'baidu' ? 'bg-green-50 dark:bg-green-900/20' :
            processorType === 'backend' ? 'bg-purple-50 dark:bg-purple-900/20' :
            'bg-orange-50 dark:bg-orange-900/20'
          }`}>
            <div className="flex items-start space-x-2">
              <CheckCircle2 className={`h-4 w-4 mt-0.5 flex-shrink-0 ${
                processorType === 'baidu' ? 'text-green-600' :
                processorType === 'backend' ? 'text-purple-600' :
                'text-orange-600'
              }`} />
              <div className="text-sm">
                <p className={`font-medium ${
                  processorType === 'baidu' ? 'text-green-800 dark:text-green-200' :
                  processorType === 'backend' ? 'text-purple-800 dark:text-purple-200' :
                  'text-orange-800 dark:text-orange-200'
                }`}>
                  {processorType === 'baidu' ? '百度OCR服务' :
                   processorType === 'backend' ? '后端OCR服务' :
                   'OCR处理器 (演示模式)'}
                </p>
                <p className={`mt-1 ${
                  processorType === 'baidu' ? 'text-green-600 dark:text-green-300' :
                  processorType === 'backend' ? 'text-purple-600 dark:text-purple-300' :
                  'text-orange-600 dark:text-orange-300'
                }`}>
                  {processorType === 'baidu'
                    ? '使用百度智能云OCR API进行高精度字幕识别，支持多语言文本检测和识别，具有更好的性能和准确率。'
                    : processorType === 'backend'
                    ? '使用后端PaddleOCR服务进行高精度字幕识别，支持多语言文本检测和识别，具有更好的性能和准确率。'
                    : '当前使用演示模式OCR处理器，提供模拟的字幕识别功能用于演示和测试。'
                  }
                </p>
              </div>
            </div>
          </div>

          {/* 可滚动内容区域 */}
          <ScrollArea className="flex-1">
            {/* 设置面板 */}
            <div className="p-4 border-b">
            <h3 className="font-medium mb-4 flex items-center">
              <Settings className="h-4 w-4 mr-2" />
              提取设置
            </h3>

            <div className="space-y-4">
              {/* FPS设置 */}
              <div>
                <Label htmlFor="fps" className="text-sm">检测频率 (FPS)</Label>
                <Input
                  id="fps"
                  type="number"
                  value={fps}
                  onChange={(e) => setFps(Number(e.target.value))}
                  min={1}
                  max={60}
                  className="mt-1"
                />
                <p className="text-xs text-muted-foreground mt-1">
                  每秒检测帧数，越高越精确但速度越慢
                </p>
              </div>

              {/* 最短字幕时长 */}
              <div>
                <Label htmlFor="minDuration" className="text-sm">最短字幕时长 (毫秒)</Label>
                <Input
                  id="minDuration"
                  type="number"
                  value={minSubtitleDuration}
                  onChange={(e) => setMinSubtitleDuration(Number(e.target.value))}
                  min={100}
                  max={5000}
                  className="mt-1"
                />
                <p className="text-xs text-muted-foreground mt-1">
                  过滤掉过短的字幕片段
                </p>
              </div>

              {/* 自动分析选项 */}
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="autoAnalyze"
                  checked={autoAnalyze}
                  onCheckedChange={(checked) => setAutoAnalyze(checked as boolean)}
                />
                <Label htmlFor="autoAnalyze" className="text-sm">
                  上传视频时自动分析锚点
                </Label>
              </div>

              {/* 显示锚点选项 */}
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="showAnchors"
                  checked={showAnchors}
                  onCheckedChange={(checked) => setShowAnchors(checked as boolean)}
                />
                <Label htmlFor="showAnchors" className="text-sm">
                  显示字幕锚点
                </Label>
              </div>
            </div>
            </div>

            {/* 操作按钮 */}
            <div className="p-4 border-b space-y-2">
            <Button
              onClick={handleAnalyzeAnchors}
              disabled={!videoFile || progress.isRunning}
              className="w-full"
              variant="outline"
            >
              {progress.isRunning && progress.stage === 'analyzing' ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  分析中...
                </>
              ) : (
                <>
                  <Eye className="h-4 w-4 mr-2" />
                  分析字幕锚点
                </>
              )}
            </Button>

            {anchors.length > 0 && (
              <Button
                onClick={clearAllAnchors}
                variant="outline"
                className="w-full"
                disabled={progress.isRunning}
              >
                <Trash2 className="h-4 w-4 mr-2" />
                清除所有锚点
              </Button>
            )}

            <div className="space-y-2">
              {/* 提取状态提示 */}
              {!isRealtimeMode && (
                <div className="text-xs text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-950 p-2 rounded">
                  <div className="flex items-center space-x-1">
                    <Eye className="h-3 w-3" />
                    <span>点击左下角的"开始实时识别"按钮开始字幕提取</span>
                  </div>
                  <div className="text-xs mt-1 text-blue-500">
                    注：实时识别模式只用于显示锚点，不会调用OCR API
                  </div>
                </div>
              )}

              {isRealtimeMode && (
                <div className="text-xs text-green-600 dark:text-green-400 bg-green-50 dark:bg-green-950 p-2 rounded">
                  <div className="flex items-center space-x-1">
                    <CheckCircle2 className="h-3 w-3" />
                    <span>实时识别模式已开启，正在进行拼图提取...</span>
                  </div>
                  <div className="text-xs mt-1 text-green-500">
                    拼图提取策略：按秒截取 → 拼图合并 → 最后才调用OCR API
                  </div>
                </div>
              )}

              {/* API限制提示 */}
              {apiLimitReached && (
                <div className="text-xs text-red-600 dark:text-red-400 bg-red-50 dark:bg-red-950 p-2 rounded border border-red-200 dark:border-red-800">
                  <div className="flex items-center space-x-1 mb-2">
                    <AlertTriangle className="h-3 w-3" />
                    <span className="font-medium">百度OCR API每日限制已达到</span>
                  </div>
                  <div className="text-xs space-y-1">
                    <div>• 请切换到"后端OCR"或"简单OCR"继续使用</div>
                    <div>• 或者明天再使用百度OCR服务</div>
                    <div>• 百度OCR免费版每日限制：500次调用</div>
                  </div>
                </div>
              )}

              {ocrProcessor instanceof BaiduOCRProcessor && !apiLimitReached && (
                <div className="text-xs text-orange-600 dark:text-orange-400 bg-orange-50 dark:bg-orange-950 p-2 rounded">
                  <div className="flex items-center space-x-1">
                    <AlertTriangle className="h-3 w-3" />
                    <span>如果遇到API限制，可以切换到后端OCR或简单OCR服务</span>
                  </div>
                </div>
              )}

              {progress.isRunning && (
                <Button
                  variant="destructive"
                  onClick={handleStopExtraction}
                  className="w-full"
                >
                  <AlertTriangle className="h-4 w-4 mr-2" />
                  停止提取
                </Button>
              )}

              <Button
                variant="outline"
                onClick={testCurrentFrame}
                disabled={testingAnchor || !ocrProcessor || anchors.length === 0}
                className="flex-shrink-0"
              >
                {testingAnchor ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    测试中...
                  </>
                ) : (
                  <>
                    <ImageIcon className="h-4 w-4 mr-2" />
                    测试当前帧
                  </>
                )}
              </Button>

              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  id="smartExtraction"
                  checked={smartExtractionConfig.enabled}
                  onChange={(e) => setSmartExtractionConfig(prev => ({
                    ...prev,
                    enabled: e.target.checked
                  }))}
                  className="rounded"
                />
                <label htmlFor="smartExtraction" className="text-sm">
                  启用智能提取模式
                </label>
              </div>

              {smartExtractionConfig.enabled && !(ocrProcessor instanceof BaiduOCRProcessor) && (
                <div className="text-xs text-muted-foreground bg-blue-50 dark:bg-blue-900/20 p-2 rounded">
                  ⚡ 智能模式：批量处理 + 相似帧跳过 + 自适应采样
                </div>
              )}

              {/* 拼图提取配置说明 */}
              {ocrProcessor instanceof BaiduOCRProcessor && isRealtimeMode && (
                <div className="space-y-3 p-3 bg-blue-50 dark:bg-blue-950 rounded-lg border border-blue-200 dark:border-blue-800">
                  <div className="flex items-center space-x-2">
                    <CheckCircle2 className="h-4 w-4 text-blue-600" />
                    <span className="text-sm font-medium text-blue-800 dark:text-blue-200">
                      拼图提取策略
                    </span>
                  </div>

                  <div className="text-xs text-blue-600 dark:text-blue-400 space-y-1">
                    <div>📸 第1步：按每秒截取字幕锚点区域图片</div>
                    <div>🧩 第2步：将截取图片拼图合并为3-5张高清图</div>
                    <div>🔍 第3步：百度OCR通用文字识别（标准版）</div>
                    <div>🗺️ 第4步：结果映射回正确的时间戳</div>
                  </div>

                  <div className="text-xs text-blue-600 dark:text-blue-400">
                    💡 优势：按秒截取 → 拼图合并 → 减少API调用次数 → 保持高识别准确率
                  </div>
                </div>
              )}
            </div>

            {subtitles.length > 0 && (
              <Button
                onClick={() => {
                  const srtContent = exportToSRT(subtitles)
                  const blob = new Blob([srtContent], { type: 'text/plain' })
                  const url = URL.createObjectURL(blob)
                  const a = document.createElement('a')
                  a.href = url
                  a.download = `${videoFile?.name.replace(/\.[^/.]+$/, '')}.srt`
                  a.click()
                  URL.revokeObjectURL(url)
                  toast.success('字幕文件已导出')
                }}
                variant="secondary"
                className="w-full"
              >
                <Download className="h-4 w-4 mr-2" />
                导出SRT字幕
              </Button>
            )}
            </div>

            {/* 进度显示 */}
            {progress.isRunning && (
              <div className="p-4 border-b">
              <div className="space-y-2">
                <div className="flex items-center justify-between text-sm">
                  <span>
                    {progress.stage === 'analyzing' ? '分析进度' : '提取进度'}
                  </span>
                  <span>{progress.current || Math.round((progress.currentFrame / progress.totalFrames) * 100)}%</span>
                </div>
                <Progress value={progress.current || (progress.currentFrame / progress.totalFrames) * 100} />
                <div className="flex items-center justify-between text-xs text-muted-foreground">
                  <span>时间点: {progress.currentFrame}/{progress.totalFrames}秒</span>
                  <span>速度: {progress.speed.toFixed(1)}x</span>
                </div>
                <div className="flex items-center justify-between text-xs text-muted-foreground">
                  <span>时间: {formatTime(progress.currentTime)} / {formatTime(progress.totalTime)}</span>
                  {progress.totalSubtitles !== undefined && (
                    <span className="text-green-600 font-medium">
                      📝 已提取: {progress.totalSubtitles} 条字幕
                    </span>
                  )}
                </div>
                {performanceStats.totalProcessedFrames > 0 && (
                  <div className="text-xs text-muted-foreground space-y-1 pt-2 border-t">
                    <div>平均处理时间: {performanceStats.avgProcessingTime.toFixed(1)}ms</div>
                    <div>加速比: {performanceStats.speedupRatio.toFixed(1)}x</div>
                    {performanceStats.skippedFrames > 0 && (
                      <div className="text-blue-600">
                        🚀 跳过相似帧: {performanceStats.skippedFrames} 帧
                      </div>
                    )}
                    {performanceStats.cacheHitRate > 0 && (
                      <div className="text-purple-600">
                        💾 缓存命中率: {(performanceStats.cacheHitRate * 100).toFixed(1)}%
                      </div>
                    )}
                    <div className="text-green-600">
                      ⚡ {smartExtractionConfig.enabled ? '智能提取模式' : '区域识别模式'} - 速度提升 {Math.round((performanceStats.speedupRatio - 1) * 100)}%
                    </div>
                  </div>
                )}
              </div>
              </div>
            )}

            {/* 字幕锚点列表 */}
            {anchors.length > 0 && (
              <div className="p-4 border-b">
              <h3 className="font-medium mb-3 flex items-center">
                <ImageIcon className="h-4 w-4 mr-2" />
                字幕锚点 ({anchors.length})
              </h3>
              <ScrollArea className="max-h-48">
                <div className="space-y-2">
                  {anchors.map((anchor, index) => (
                    <Card key={anchor.id} className="p-3">
                      <div className="space-y-2">
                        <div className="flex items-center justify-between">
                          <span className="text-sm font-medium">锚点 {index + 1}</span>
                          <div className="flex items-center space-x-2">
                            <div
                              className="w-4 h-4 rounded border"
                              style={{ backgroundColor: anchor.color }}
                            />
                            {anchor.originalWidth && (
                              <Button
                                size="sm"
                                variant="ghost"
                                onClick={() => {
                                  // 重置锚点到原始状态
                                  setAnchors(anchors.map(a =>
                                    a.id === anchor.id ? {
                                      ...a,
                                      centerX: a.centerX, // 保持当前中心位置
                                      centerY: a.centerY,
                                      width: a.originalWidth!,
                                      height: a.originalHeight!,
                                      lastDetectedBounds: undefined
                                    } : a
                                  ))
                                }}
                                title="重置锚点大小"
                              >
                                <RotateCcw className="h-3 w-3" />
                              </Button>
                            )}
                            <Button
                              size="sm"
                              variant="ghost"
                              onClick={() => {
                                setAnchors(anchors.filter(a => a.id !== anchor.id))
                              }}
                            >
                              <Trash2 className="h-3 w-3" />
                            </Button>
                          </div>
                        </div>

                        <div className="grid grid-cols-2 gap-2 text-xs">
                          <div>
                            <Label>X: {Math.round(anchor.centerX)}</Label>
                          </div>
                          <div>
                            <Label>Y: {Math.round(anchor.centerY)}</Label>
                          </div>
                          <div>
                            <Label>宽: {Math.round(anchor.width)}</Label>
                          </div>
                          <div>
                            <Label>高: {Math.round(anchor.height)}</Label>
                          </div>
                        </div>

                        {anchor.lastDetectedBounds && (
                          <div className="text-xs text-green-600 bg-green-50 p-2 rounded">
                            🔄 已动态调整
                            {anchor.originalWidth && (
                              <div className="text-gray-500">
                                原始: {Math.round(anchor.originalWidth)} × {Math.round(anchor.originalHeight)}
                              </div>
                            )}
                          </div>
                        )}

                        <div className="space-y-2">
                          <Select
                            value={anchor.language}
                            onValueChange={(value) => {
                              setAnchors(anchors.map(a =>
                                a.id === anchor.id ? { ...a, language: value } : a
                              ))
                            }}
                          >
                            <SelectTrigger className="h-8">
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              {LANGUAGE_OPTIONS.map(lang => (
                                <SelectItem key={lang.value} value={lang.value}>
                                  {lang.label}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>

                          <div className="flex items-center justify-between">
                            <div className="flex items-center space-x-2">
                              <Checkbox
                                id={`primary-${anchor.id}`}
                                checked={anchor.isPrimary}
                                onCheckedChange={(checked) => {
                                  setAnchors(anchors.map(a =>
                                    a.id === anchor.id ? { ...a, isPrimary: checked as boolean } : a
                                  ))
                                }}
                              />
                              <Label htmlFor={`primary-${anchor.id}`} className="text-xs">
                                主字幕
                              </Label>
                            </div>

                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => testSingleAnchor(anchor)}
                              disabled={testingAnchor || !ocrProcessor}
                              className="h-6 px-2 text-xs"
                            >
                              {testingAnchor ? (
                                <Loader2 className="h-3 w-3 animate-spin" />
                              ) : (
                                '测试'
                              )}
                            </Button>
                          </div>
                        </div>
                      </div>
                    </Card>
                  ))}
                </div>
              </ScrollArea>
              </div>
            )}

            {/* 调试信息 */}
            {debugImages.length > 0 && (
              <div className="p-4 border-b">
                <h3 className="font-medium mb-3 flex items-center">
                  <ImageIcon className="h-4 w-4 mr-2" />
                  锚点测试结果
                </h3>
                <ScrollArea className="max-h-64">
                  <div className="space-y-3">
                    {debugImages.map((debug, index) => (
                      <Card key={index} className="p-3">
                        <div className="space-y-2">
                          <div className="flex items-center justify-between">
                            <span className="text-sm font-medium">锚点 {debug.anchorId}</span>
                            <span className="text-xs text-muted-foreground">
                              {debug.ocrResults.length} 个结果
                            </span>
                          </div>

                          <div className="flex space-x-3">
                            <div className="flex-shrink-0">
                              <img
                                src={debug.croppedImage}
                                alt="裁剪区域"
                                className="w-20 h-12 object-contain border rounded"
                              />
                              <p className="text-xs text-center mt-1">裁剪区域</p>
                            </div>

                            <div className="flex-1 min-w-0">
                              {debug.ocrResults.length > 0 ? (
                                <div className="space-y-1">
                                  {debug.ocrResults.map((result, i) => (
                                    <div key={i} className="text-xs">
                                      <span className="font-medium">"{result.text}"</span>
                                      <span className="text-muted-foreground ml-2">
                                        ({Math.round(result.confidence * 100)}%)
                                      </span>
                                    </div>
                                  ))}
                                </div>
                              ) : (
                                <p className="text-xs text-muted-foreground">未识别到文本</p>
                              )}
                            </div>
                          </div>
                        </div>
                      </Card>
                    ))}
                  </div>
                </ScrollArea>
              </div>
            )}

            {/* 字幕结果列表 */}
            <div className="border-t">
              <div className="p-4 border-b">
                <h3 className="font-medium flex items-center">
                  <FileText className="h-4 w-4 mr-2" />
                  提取结果 ({subtitles.length})
                </h3>
              </div>

              <div className="p-4 space-y-2 max-h-96 overflow-y-auto">
                {subtitles.length === 0 ? (
                  <div className="text-center text-muted-foreground py-8">
                    <FileText className="h-8 w-8 mx-auto mb-2 opacity-50" />
                    <p className="text-sm">暂无字幕结果</p>
                    <p className="text-xs">请先分析锚点并开始提取</p>
                  </div>
                ) : (
                  subtitles.map((subtitle) => (
                    <Card
                      key={subtitle.id}
                      className={`p-3 cursor-pointer transition-colors ${
                        selectedSubtitle === subtitle.id
                          ? 'bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-800'
                          : 'hover:bg-gray-50 dark:hover:bg-gray-800'
                      }`}
                      onClick={() => {
                        setSelectedSubtitle(subtitle.id)
                        seekTo(subtitle.startTime)
                      }}
                    >
                      <div className="space-y-2">
                        <div className="flex items-center justify-between text-xs text-muted-foreground">
                          <span>
                            {formatTime(subtitle.startTime)} → {formatTime(subtitle.endTime)}
                          </span>
                          <Badge variant="outline" className="text-xs">
                            {Math.round(subtitle.confidence * 100)}%
                          </Badge>
                        </div>
                        <p className="text-sm leading-relaxed">{subtitle.text}</p>
                      </div>
                    </Card>
                  ))
                )}
              </div>
            </div>
          </ScrollArea>
        </div>
      </div>
    </div>
  )
}
