/**
 * 字幕拼图处理器
 * 负责截取字幕锚点区域、拼图合并、OCR调用和结果映射
 */

export interface AnchorFrame {
  timestamp: number
  imageData: ImageData
  anchorId: string
  anchorRect: {
    x: number
    y: number
    width: number
    height: number
  }
}

export interface MosaicImage {
  imageData: ImageData
  startTime: number
  endTime: number
  frameCount: number
  frames: AnchorFrame[]
  layout: {
    rows: number
    cols: number
    frameWidth: number
    frameHeight: number
  }
}

export interface MappedSubtitle {
  timestamp: number
  text: string
  confidence: number
  anchorId: string
}

export class SubtitleMosaicProcessor {
  private maxImageSize = 8 * 1024 * 1024 // 8MB
  private maxDimension = 4096 // 最大边长
  private minDimension = 15 // 最小边长
  
  /**
   * 第1步：批量截取标记的字幕锚点区域图片
   */
  async extractAnchorFrames(
    video: HTMLVideoElement,
    anchors: Array<{ id: string; x: number; y: number; width: number; height: number; isPrimary: boolean }>,
    duration: number,
    onProgress?: (progress: number) => void
  ): Promise<AnchorFrame[]> {
    console.log('🎬 开始批量截取字幕锚点区域...')
    
    const frames: AnchorFrame[] = []
    const primaryAnchors = anchors.filter(a => a.isPrimary)
    
    if (primaryAnchors.length === 0) {
      throw new Error('没有主锚点可用于提取')
    }
    
    const totalSeconds = Math.floor(duration)
    
    for (let second = 0; second < totalSeconds; second++) {
      try {
        // 设置视频时间
        video.currentTime = second
        await this.waitForVideoSeek(video, second)
        
        // 为每个主锚点截取区域
        for (const anchor of primaryAnchors) {
          const frameData = await this.extractAnchorRegion(video, anchor)
          
          frames.push({
            timestamp: second,
            imageData: frameData,
            anchorId: anchor.id,
            anchorRect: {
              x: anchor.x,
              y: anchor.y,
              width: anchor.width,
              height: anchor.height
            }
          })
        }
        
        // 更新进度
        if (onProgress) {
          onProgress((second + 1) / totalSeconds)
        }
        
      } catch (error) {
        console.warn(`截取第${second}秒帧失败:`, error)
      }
    }
    
    console.log(`✅ 截取完成，共获得 ${frames.length} 个锚点帧`)
    return frames
  }
  
  /**
   * 第2步：将截取的字幕锚点区域图片进行拼图合并
   */
  async createMosaicImages(frames: AnchorFrame[]): Promise<MosaicImage[]> {
    console.log('🧩 开始创建拼图...')
    
    if (frames.length === 0) {
      return []
    }
    
    // 计算每张拼图的最大帧数
    const sampleFrame = frames[0]
    const frameSize = sampleFrame.imageData.width * sampleFrame.imageData.height * 4 // RGBA
    const maxFramesPerMosaic = this.calculateMaxFramesPerMosaic(frameSize)
    
    console.log(`每张拼图最多包含 ${maxFramesPerMosaic} 个帧`)
    
    const mosaics: MosaicImage[] = []
    
    // 将帧分组
    for (let i = 0; i < frames.length; i += maxFramesPerMosaic) {
      const groupFrames = frames.slice(i, i + maxFramesPerMosaic)
      const mosaic = await this.createSingleMosaic(groupFrames)
      
      if (mosaic) {
        mosaics.push(mosaic)
      }
    }
    
    console.log(`✅ 创建了 ${mosaics.length} 张拼图`)
    return mosaics
  }
  
  /**
   * 第4步：将OCR结果映射回时间戳
   */
  mapOCRResultsToTimestamps(
    ocrResults: Array<{ text: string; confidence: number; bbox?: number[][] }>,
    mosaic: MosaicImage
  ): MappedSubtitle[] {
    console.log('🗺️ 开始映射OCR结果到时间戳...')
    
    const mappedSubtitles: MappedSubtitle[] = []
    
    if (!ocrResults || ocrResults.length === 0) {
      return mappedSubtitles
    }
    
    // 按y坐标排序OCR结果（从上到下）
    const sortedResults = ocrResults
      .filter(result => result.text && result.text.trim())
      .sort((a, b) => {
        const aY = a.bbox ? a.bbox[0][1] : 0
        const bY = b.bbox ? b.bbox[0][1] : 0
        return aY - bY
      })
    
    // 将OCR结果平均分配给时间段
    const timeRange = mosaic.endTime - mosaic.startTime
    const timePerResult = timeRange / Math.max(sortedResults.length, 1)
    
    sortedResults.forEach((result, index) => {
      const timestamp = mosaic.startTime + (index * timePerResult)
      
      mappedSubtitles.push({
        timestamp: Math.round(timestamp),
        text: result.text.trim(),
        confidence: result.confidence || 0.9,
        anchorId: mosaic.frames[0]?.anchorId || 'unknown'
      })
    })
    
    console.log(`✅ 映射了 ${mappedSubtitles.length} 个字幕`)
    return mappedSubtitles
  }
  
  /**
   * 等待视频跳转到指定时间
   */
  private async waitForVideoSeek(video: HTMLVideoElement, targetTime: number): Promise<void> {
    return new Promise((resolve) => {
      const checkTime = () => {
        if (Math.abs(video.currentTime - targetTime) < 0.1) {
          resolve()
        } else {
          requestAnimationFrame(checkTime)
        }
      }
      checkTime()
    })
  }
  
  /**
   * 截取锚点区域
   */
  private async extractAnchorRegion(
    video: HTMLVideoElement,
    anchor: { x: number; y: number; width: number; height: number }
  ): Promise<ImageData> {
    const canvas = document.createElement('canvas')
    const ctx = canvas.getContext('2d')!
    
    // 设置画布大小为锚点区域大小
    canvas.width = anchor.width
    canvas.height = anchor.height
    
    // 绘制锚点区域
    ctx.drawImage(
      video,
      anchor.x, anchor.y, anchor.width, anchor.height,
      0, 0, anchor.width, anchor.height
    )
    
    return ctx.getImageData(0, 0, anchor.width, anchor.height)
  }
  
  /**
   * 计算每张拼图的最大帧数
   */
  private calculateMaxFramesPerMosaic(frameSize: number): number {
    // 考虑base64编码增加33%的大小
    const maxRawSize = this.maxImageSize * 0.75 // 留一些余量
    const maxFrames = Math.floor(maxRawSize / frameSize)

    // 至少1帧，最多200帧
    return Math.max(1, Math.min(maxFrames, 200))
  }

  /**
   * 创建单张拼图
   */
  private async createSingleMosaic(frames: AnchorFrame[]): Promise<MosaicImage | null> {
    if (frames.length === 0) {
      return null
    }

    const firstFrame = frames[0]
    const frameWidth = firstFrame.imageData.width
    const frameHeight = firstFrame.imageData.height

    // 计算最优布局（垂直排列）
    const layout = this.calculateOptimalLayout(frames.length, frameWidth, frameHeight)

    // 创建拼图画布
    const canvas = document.createElement('canvas')
    const ctx = canvas.getContext('2d')!

    canvas.width = layout.cols * layout.frameWidth
    canvas.height = layout.rows * layout.frameHeight

    // 检查尺寸限制
    if (canvas.width > this.maxDimension || canvas.height > this.maxDimension) {
      console.warn('拼图尺寸超出限制，进行缩放')
      const scale = Math.min(this.maxDimension / canvas.width, this.maxDimension / canvas.height)
      canvas.width *= scale
      canvas.height *= scale
      layout.frameWidth *= scale
      layout.frameHeight *= scale
    }

    // 绘制每个帧到拼图上
    for (let i = 0; i < frames.length; i++) {
      const frame = frames[i]
      const row = Math.floor(i / layout.cols)
      const col = i % layout.cols

      const x = col * layout.frameWidth
      const y = row * layout.frameHeight

      // 创建临时画布来绘制帧
      const tempCanvas = document.createElement('canvas')
      const tempCtx = tempCanvas.getContext('2d')!
      tempCanvas.width = frame.imageData.width
      tempCanvas.height = frame.imageData.height
      tempCtx.putImageData(frame.imageData, 0, 0)

      // 绘制到拼图画布上
      ctx.drawImage(tempCanvas, x, y, layout.frameWidth, layout.frameHeight)
    }

    // 获取拼图的ImageData
    const mosaicImageData = ctx.getImageData(0, 0, canvas.width, canvas.height)

    return {
      imageData: mosaicImageData,
      startTime: frames[0].timestamp,
      endTime: frames[frames.length - 1].timestamp,
      frameCount: frames.length,
      frames: frames,
      layout: layout
    }
  }

  /**
   * 计算最优布局
   */
  private calculateOptimalLayout(frameCount: number, frameWidth: number, frameHeight: number) {
    // 优先使用垂直排列（单列）
    const cols = 1
    const rows = frameCount

    return {
      rows,
      cols,
      frameWidth,
      frameHeight
    }
  }

  /**
   * 将ImageData转换为base64
   */
  async imageDataToBase64(imageData: ImageData): Promise<string> {
    const canvas = document.createElement('canvas')
    const ctx = canvas.getContext('2d')!

    canvas.width = imageData.width
    canvas.height = imageData.height
    ctx.putImageData(imageData, 0, 0)

    return canvas.toDataURL('image/png')
  }
}
