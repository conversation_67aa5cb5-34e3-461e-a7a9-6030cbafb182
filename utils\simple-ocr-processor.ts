// 简化的OCR处理器，用于测试和备用方案
export interface OCRResult {
  text: string
  confidence: number
  bbox: number[][]
}

export class SimpleOCRProcessor {
  private isInitialized = false
  private mockTexts = [
    "欢迎观看本期节目",
    "今天我们来聊聊人工智能", 
    "机器学习正在改变世界",
    "深度学习的应用越来越广泛",
    "让我们一起探索科技的未来",
    "感谢大家的观看",
    "下期节目再见",
    "请关注我们的频道",
    "订阅获取更多精彩内容",
    "人工智能助手正在帮助人类",
    "技术创新推动社会进步",
    "数字化转型势在必行"
  ]
  private currentTextIndex = 0

  async initialize(): Promise<void> {
    console.log('正在初始化简化OCR处理器...')
    
    // 模拟加载延迟
    await new Promise(resolve => setTimeout(resolve, 1500))
    
    this.isInitialized = true
    console.log('简化OCR处理器初始化完成')
  }

  async processImage(imageData: ImageData): Promise<OCRResult[]> {
    if (!this.isInitialized) {
      throw new Error('处理器未初始化')
    }

    // 模拟处理延迟
    await new Promise(resolve => setTimeout(resolve, 50 + Math.random() * 100))

    // 随机决定是否检测到文本（模拟真实场景中的空白帧）
    const hasText = Math.random() > 0.3 // 70%的概率检测到文本

    if (!hasText) {
      return []
    }

    // 获取当前文本
    const text = this.mockTexts[this.currentTextIndex % this.mockTexts.length]
    this.currentTextIndex++

    // 生成模拟的边界框（通常在视频底部）
    const bbox: number[][] = [
      [imageData.width * 0.1, imageData.height * 0.85],
      [imageData.width * 0.9, imageData.height * 0.85],
      [imageData.width * 0.9, imageData.height * 0.95],
      [imageData.width * 0.1, imageData.height * 0.95]
    ]

    // 模拟置信度（85-98%之间）
    const confidence = 0.85 + Math.random() * 0.13

    return [
      {
        text,
        confidence,
        bbox
      }
    ]
  }

  // 模拟锚点检测
  async detectAnchors(imageData: ImageData): Promise<{
    centerX: number
    centerY: number
    width: number
    height: number
    confidence: number
  }[]> {
    if (!this.isInitialized) {
      throw new Error('处理器未初始化')
    }

    // 模拟检测延迟
    await new Promise(resolve => setTimeout(resolve, 200))

    // 返回模拟的字幕锚点（通常在视频底部中央）
    return [
      {
        centerX: imageData.width * 0.5,
        centerY: imageData.height * 0.9,
        width: imageData.width * 0.8,
        height: imageData.height * 0.1,
        confidence: 0.92
      }
    ]
  }

  // 重置文本索引（用于新视频）
  reset(): void {
    this.currentTextIndex = 0
  }

  dispose(): void {
    this.isInitialized = false
    console.log('简化OCR处理器已释放')
  }

  // 获取处理器状态
  getStatus(): {
    isInitialized: boolean
    type: string
    version: string
  } {
    return {
      isInitialized: this.isInitialized,
      type: 'Simple OCR Processor',
      version: '1.0.0'
    }
  }
}
