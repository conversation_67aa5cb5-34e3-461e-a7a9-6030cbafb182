import { NextApiRequest, NextApiResponse } from 'next'

/**
 * 获取百度OCR API密钥
 */
async function getBaiduOcrApiKey(): Promise<string | null> {
  // 优先从环境变量获取
  if (process.env.BAIDU_OCR_API_KEY) {
    return process.env.BAIDU_OCR_API_KEY
  }

  // 尝试从Docker配置获取
  try {
    const { DockerConfigManager } = await import('@/lib/docker-config-manager')
    return DockerConfigManager.getBaiduOcrApiKey()
  } catch (error) {
    console.warn('无法加载Docker配置管理器:', error)
    return null
  }
}

/**
 * 获取百度OCR Secret Key
 */
async function getBaiduOcrSecretKey(): Promise<string | null> {
  // 优先从环境变量获取
  if (process.env.BAIDU_OCR_SECRET_KEY) {
    return process.env.BAIDU_OCR_SECRET_KEY
  }

  // 尝试从Docker配置获取
  try {
    const { DockerConfigManager } = await import('@/lib/docker-config-manager')
    return DockerConfigManager.getBaiduOcrSecretKey()
  } catch (error) {
    console.warn('无法加载Docker配置管理器:', error)
    return null
  }
}

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ success: false, error: '只支持POST请求' })
  }

  try {
    // 首先尝试从请求体获取密钥
    let { apiKey, secretKey } = req.body

    // 如果请求体中没有密钥，从配置中获取
    if (!apiKey || !secretKey) {
      apiKey = await getBaiduOcrApiKey()
      secretKey = await getBaiduOcrSecretKey()
    }

    if (!apiKey || !secretKey) {
      return res.status(400).json({
        success: false,
        error: 'API Key和Secret Key都是必需的，请在设置中配置百度OCR密钥'
      })
    }

    // 如果是测试密钥，直接返回端点可用
    if (apiKey === 'test' && secretKey === 'test') {
      return res.status(400).json({
        success: false,
        error: '请配置真实的百度OCR API密钥'
      })
    }

    // 获取访问令牌
    console.log('测试百度OCR API连接...')

    const tokenResponse = await fetch('https://aip.baidubce.com/oauth/2.0/token', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded'
      },
      body: new URLSearchParams({
        grant_type: 'client_credentials',
        client_id: apiKey,
        client_secret: secretKey
      })
    })

    if (!tokenResponse.ok) {
      return res.status(400).json({
        success: false,
        error: `获取访问令牌失败: HTTP ${tokenResponse.status}`
      })
    }

    const tokenData = await tokenResponse.json()

    if (tokenData.error) {
      // 提供更友好的错误信息
      let errorMessage = `百度API错误: ${tokenData.error_description || tokenData.error}`

      if (tokenData.error === 'invalid_client') {
        errorMessage = 'API Key或Secret Key无效，请检查密钥是否正确'
      } else if (tokenData.error === 'unsupported_grant_type') {
        errorMessage = '授权类型不支持，请检查API配置'
      }

      return res.status(400).json({
        success: false,
        error: errorMessage
      })
    }

    if (!tokenData.access_token) {
      return res.status(400).json({
        success: false,
        error: '未能获取有效的访问令牌，请检查API密钥'
      })
    }

    // 测试成功
    console.log('百度OCR API连接测试成功')

    return res.status(200).json({
      success: true,
      message: '百度OCR API连接正常',
      tokenInfo: {
        expires_in: tokenData.expires_in,
        scope: tokenData.scope
      }
    })

  } catch (error) {
    console.error('百度OCR API测试失败:', error)
    
    return res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : '未知错误'
    })
  }
}
