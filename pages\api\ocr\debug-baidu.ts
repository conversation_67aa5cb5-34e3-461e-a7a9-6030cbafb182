import { NextApiRequest, NextApiResponse } from 'next'

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'GET') {
    return res.status(405).json({ success: false, error: '只支持GET请求' })
  }

  try {
    console.log('🔍 调试百度OCR配置...')

    // 检查环境变量
    const envApiKey = process.env.BAIDU_OCR_API_KEY
    const envSecretKey = process.env.BAIDU_OCR_SECRET_KEY

    console.log(`环境变量检查:`)
    console.log(`- BAIDU_OCR_API_KEY: ${envApiKey ? '已设置' : '未设置'}`)
    console.log(`- BAIDU_OCR_SECRET_KEY: ${envSecretKey ? '已设置' : '未设置'}`)

    // 检查Docker配置
    let dockerApiKey = null
    let dockerSecretKey = null
    
    try {
      const { DockerConfigManager } = await import('@/lib/docker-config-manager')
      dockerApiKey = DockerConfigManager.getBaiduOcrApiKey()
      dockerSecretKey = DockerConfigManager.getBaiduOcrSecretKey()
      
      console.log(`Docker配置检查:`)
      console.log(`- Docker API Key: ${dockerApiKey ? '已设置' : '未设置'}`)
      console.log(`- Docker Secret Key: ${dockerSecretKey ? '已设置' : '未设置'}`)
    } catch (error) {
      console.log(`Docker配置检查失败:`, error)
    }

    // 最终使用的密钥
    const finalApiKey = envApiKey || dockerApiKey
    const finalSecretKey = envSecretKey || dockerSecretKey

    console.log(`最终密钥:`)
    console.log(`- 最终 API Key: ${finalApiKey ? '已配置' : '未配置'}`)
    console.log(`- 最终 Secret Key: ${finalSecretKey ? '已配置' : '未配置'}`)

    // 如果有密钥，测试获取访问令牌
    let tokenTest = null
    if (finalApiKey && finalSecretKey) {
      try {
        console.log('🔑 测试获取访问令牌...')
        
        const tokenResponse = await fetch('https://aip.baidubce.com/oauth/2.0/token', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded'
          },
          body: new URLSearchParams({
            grant_type: 'client_credentials',
            client_id: finalApiKey,
            client_secret: finalSecretKey
          })
        })

        const tokenData = await tokenResponse.json()
        
        if (tokenData.error) {
          tokenTest = {
            success: false,
            error: `${tokenData.error}: ${tokenData.error_description || '未知错误'}`
          }
        } else if (tokenData.access_token) {
          tokenTest = {
            success: true,
            expires_in: tokenData.expires_in,
            scope: tokenData.scope
          }
        } else {
          tokenTest = {
            success: false,
            error: '未能获取访问令牌'
          }
        }
      } catch (error) {
        tokenTest = {
          success: false,
          error: error instanceof Error ? error.message : '网络错误'
        }
      }
    }

    return res.status(200).json({
      success: true,
      debug: {
        environment: {
          hasApiKey: !!envApiKey,
          hasSecretKey: !!envSecretKey
        },
        docker: {
          hasApiKey: !!dockerApiKey,
          hasSecretKey: !!dockerSecretKey
        },
        final: {
          hasApiKey: !!finalApiKey,
          hasSecretKey: !!finalSecretKey,
          source: envApiKey ? 'environment' : dockerApiKey ? 'docker' : 'none'
        },
        tokenTest
      }
    })

  } catch (error) {
    console.error('调试百度OCR配置失败:', error)
    
    return res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : '未知错误'
    })
  }
}
