// 智能帧提取器 - 基于SubtitleOCR的优化策略
export interface FrameData {
  timestamp: number
  imageData: string
  hash?: string
  similarity?: number
}

export interface ExtractionConfig {
  fps: number
  skipSimilarFrames: boolean
  similarityThreshold: number
  batchSize: number
  maxConcurrentBatches: number
  adaptiveSampling: boolean
}

export class SmartFrameExtractor {
  private video: HTMLVideoElement
  private canvas: HTMLCanvasElement
  private ctx: CanvasRenderingContext2D
  private config: ExtractionConfig
  private frameCache: Map<number, FrameData> = new Map()
  private lastFrameHash: string | null = null

  constructor(video: HTMLVideoElement, config: Partial<ExtractionConfig> = {}) {
    this.video = video
    this.canvas = document.createElement('canvas')
    this.ctx = this.canvas.getContext('2d')!

    this.config = {
      fps: 5,
      skipSimilarFrames: true,
      similarityThreshold: 0.95,
      batchSize: 10,
      maxConcurrentBatches: 3,
      adaptiveSampling: true,
      ...config
    }
  }

  // 更新配置
  updateConfig(newConfig: Partial<ExtractionConfig>) {
    this.config = { ...this.config, ...newConfig }
  }

  // 获取当前配置
  getConfig(): ExtractionConfig {
    return { ...this.config }
  }

  // 计算图像哈希用于相似度检测
  private calculateImageHash(imageData: ImageData): string {
    const { data, width, height } = imageData
    
    // 转换为灰度并缩放到8x8
    const grayData: number[] = []
    for (let i = 0; i < data.length; i += 4) {
      const gray = Math.round(0.299 * data[i] + 0.587 * data[i + 1] + 0.114 * data[i + 2])
      grayData.push(gray)
    }
    
    // 简化的感知哈希算法
    const blockSize = Math.floor(Math.sqrt(grayData.length) / 8)
    const hash: string[] = []
    
    for (let y = 0; y < 8; y++) {
      for (let x = 0; x < 8; x++) {
        let sum = 0
        let count = 0
        
        for (let by = 0; by < blockSize; by++) {
          for (let bx = 0; bx < blockSize; bx++) {
            const idx = (y * blockSize + by) * width + (x * blockSize + bx)
            if (idx < grayData.length) {
              sum += grayData[idx]
              count++
            }
          }
        }
        
        const avg = count > 0 ? sum / count : 0
        hash.push(avg > 128 ? '1' : '0')
      }
    }
    
    return hash.join('')
  }

  // 计算两个哈希的相似度
  private calculateSimilarity(hash1: string, hash2: string): number {
    if (hash1.length !== hash2.length) return 0
    
    let sameCount = 0
    for (let i = 0; i < hash1.length; i++) {
      if (hash1[i] === hash2[i]) sameCount++
    }
    
    return sameCount / hash1.length
  }

  // 提取单帧数据
  private async extractFrame(timestamp: number): Promise<FrameData> {
    return new Promise((resolve, reject) => {
      this.video.currentTime = timestamp
      
      const onSeeked = () => {
        this.video.removeEventListener('seeked', onSeeked)
        
        try {
          // 设置canvas尺寸
          this.canvas.width = this.video.videoWidth
          this.canvas.height = this.video.videoHeight
          
          // 绘制视频帧
          this.ctx.drawImage(this.video, 0, 0)
          
          // 获取图像数据
          const imageData = this.ctx.getImageData(0, 0, this.canvas.width, this.canvas.height)
          const hash = this.calculateImageHash(imageData)
          
          // 转换为base64
          const dataURL = this.canvas.toDataURL('image/jpeg', 0.8)
          
          resolve({
            timestamp,
            imageData: dataURL,
            hash
          })
        } catch (error) {
          reject(error)
        }
      }
      
      this.video.addEventListener('seeked', onSeeked)
      
      // 超时处理
      setTimeout(() => {
        this.video.removeEventListener('seeked', onSeeked)
        reject(new Error(`提取帧 ${timestamp} 超时`))
      }, 5000)
    })
  }

  // 智能批量提取帧
  async extractFramesBatch(startTime: number, endTime: number): Promise<FrameData[]> {
    const frameInterval = 1 / this.config.fps
    const timestamps: number[] = []
    
    // 生成时间戳
    for (let time = startTime; time < endTime; time += frameInterval) {
      timestamps.push(time)
    }
    
    const frames: FrameData[] = []
    const batches: number[][] = []
    
    // 分批处理
    for (let i = 0; i < timestamps.length; i += this.config.batchSize) {
      batches.push(timestamps.slice(i, i + this.config.batchSize))
    }
    
    // 并发处理批次
    const processBatch = async (batch: number[]): Promise<FrameData[]> => {
      const batchFrames: FrameData[] = []
      
      for (const timestamp of batch) {
        try {
          const frameData = await this.extractFrame(timestamp)
          
          // 检查相似度
          if (this.config.skipSimilarFrames && this.lastFrameHash) {
            const similarity = this.calculateSimilarity(frameData.hash!, this.lastFrameHash)
            frameData.similarity = similarity
            
            if (similarity >= this.config.similarityThreshold) {
              // 跳过相似帧，但保留时间戳信息
              batchFrames.push({
                timestamp,
                imageData: '',
                hash: frameData.hash,
                similarity
              })
              continue
            }
          }
          
          batchFrames.push(frameData)
          this.lastFrameHash = frameData.hash!
          
          // 缓存帧数据
          this.frameCache.set(timestamp, frameData)
          
        } catch (error) {
          console.warn(`提取帧 ${timestamp} 失败:`, error)
        }
      }
      
      return batchFrames
    }
    
    // 限制并发数量
    const concurrentBatches: Promise<FrameData[]>[] = []
    
    for (let i = 0; i < batches.length; i += this.config.maxConcurrentBatches) {
      const currentBatches = batches.slice(i, i + this.config.maxConcurrentBatches)
      const batchPromises = currentBatches.map(processBatch)
      
      const results = await Promise.all(batchPromises)
      results.forEach(batchFrames => frames.push(...batchFrames))
    }
    
    return frames
  }

  // 自适应采样 - 根据字幕变化频率调整FPS
  async adaptiveSampling(startTime: number, endTime: number, initialFps: number = 5): Promise<FrameData[]> {
    if (!this.config.adaptiveSampling) {
      return this.extractFramesBatch(startTime, endTime)
    }
    
    const sampleDuration = 10 // 10秒采样窗口
    const frames: FrameData[] = []
    let currentFps = initialFps
    
    for (let time = startTime; time < endTime; time += sampleDuration) {
      const segmentEnd = Math.min(time + sampleDuration, endTime)
      
      // 使用当前FPS提取片段
      this.config.fps = currentFps
      const segmentFrames = await this.extractFramesBatch(time, segmentEnd)
      
      // 分析字幕变化频率
      const changeCount = this.analyzeSubtitleChanges(segmentFrames)
      const changeRate = changeCount / segmentFrames.length
      
      // 动态调整FPS
      if (changeRate > 0.3) {
        // 字幕变化频繁，提高采样率
        currentFps = Math.min(currentFps * 1.5, 20)
      } else if (changeRate < 0.1) {
        // 字幕变化稀少，降低采样率
        currentFps = Math.max(currentFps * 0.8, 2)
      }
      
      frames.push(...segmentFrames)
    }
    
    return frames
  }

  // 分析字幕变化频率
  private analyzeSubtitleChanges(frames: FrameData[]): number {
    let changes = 0
    
    for (let i = 1; i < frames.length; i++) {
      const prev = frames[i - 1]
      const curr = frames[i]
      
      if (prev.hash && curr.hash) {
        const similarity = this.calculateSimilarity(prev.hash, curr.hash)
        if (similarity < this.config.similarityThreshold) {
          changes++
        }
      }
    }
    
    return changes
  }

  // 获取缓存的帧数据
  getCachedFrame(timestamp: number): FrameData | undefined {
    return this.frameCache.get(timestamp)
  }

  // 清理缓存
  clearCache(): void {
    this.frameCache.clear()
    this.lastFrameHash = null
  }

  // 获取统计信息
  getStats(): {
    cachedFrames: number
    totalExtracted: number
    cacheHitRate: number
  } {
    return {
      cachedFrames: this.frameCache.size,
      totalExtracted: this.frameCache.size,
      cacheHitRate: this.frameCache.size > 0 ? 1 : 0
    }
  }
}
